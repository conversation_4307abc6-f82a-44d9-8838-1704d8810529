import { <PERSON>mpo<PERSON>, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, inject, signal, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { 
  IonContent, 
  IonHeader, 
  IonTitle, 
  IonToolbar, 
  IonBackButton, 
  IonButtons,
  IonSpinner,
  IonText,
  IonButton,
  IonIcon,
  IonProgressBar,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { arrowBack, refresh, alertCircle } from 'ionicons/icons';
import { PlyrModule } from 'ngx-plyr';
import * as Plyr from 'plyr';
import { VideoService, Video } from '../video.service';
import { interval, Subscription } from 'rxjs';
import { takeWhile, switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-video-player',
  standalone: true,
  imports: [
    CommonModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonBackButton,
    IonButtons,
    IonSpinner,
    IonText,
    IonButton,
    IonIcon,
    IonProgressBar,
    IonCard,
    IonCardContent,
    IonCardHeader,
    IonCardTitle,
    PlyrModule,
    RouterModule
  ],
  template: `
    <ion-header [translucent]="true">
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button defaultHref="/main-tabs/videos"></ion-back-button>
        </ion-buttons>
        <ion-title>{{ video()?.title || 'Video Player' }}</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content [fullscreen]="true">
      <!-- Loading State -->
      <div *ngIf="isLoading()" class="loading-container">
        <ion-spinner name="circles"></ion-spinner>
        <ion-text color="medium">
          <p>Loading video...</p>
        </ion-text>
      </div>

      <!-- Processing State -->
      <div *ngIf="!isLoading() && video()?.status === 'PROCESSING'" class="processing-container">
        <ion-card>
          <ion-card-header>
            <ion-card-title>{{ video()?.title }}</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <div class="processing-content">
              <ion-spinner name="circles" color="primary"></ion-spinner>
              <ion-text color="medium">
                <h3>Processing Video</h3>
                <p>Your video is being processed and will be available shortly. This usually takes a few minutes.</p>
              </ion-text>
              <ion-progress-bar type="indeterminate" color="primary"></ion-progress-bar>
              <ion-button fill="outline" (click)="checkStatus()">
                <ion-icon name="refresh" slot="start"></ion-icon>
                Check Status
              </ion-button>
            </div>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Error State -->
      <div *ngIf="!isLoading() && video()?.status === 'ERROR'" class="error-container">
        <ion-card color="danger">
          <ion-card-header>
            <ion-card-title>
              <ion-icon name="alert-circle"></ion-icon>
              Processing Error
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-text>
              <p>There was an error processing your video: {{ video()?.title }}</p>
              <p *ngIf="video()?.errorMessage" class="error-message">
                {{ video()?.errorMessage }}
              </p>
            </ion-text>
            <ion-button fill="outline" [routerLink]="['/main-tabs/videos']">
              Back to Videos
            </ion-button>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Ready State - Video Player -->
      <div *ngIf="!isLoading() && video()?.status === 'READY'" class="video-container">
        <plyr-player
          [plyrSources]="plyrSources"
          [plyrOptions]="plyrOptions"
          [plyrPoster]="video()?.thumbnailUrl"
          (plyrInit)="onPlayerInit($event)"
        ></plyr-player>
        
        <div class="video-info">
          <ion-card>
            <ion-card-header>
              <ion-card-title>{{ video()?.title }}</ion-card-title>
            </ion-card-header>
            <ion-card-content>
              <ion-text color="medium">
                <p>Duration: {{ formatDuration(video()?.duration || 0) }}</p>
                <p>File Size: {{ formatFileSize(video()?.fileSize || 0) }}</p>
                <p>Uploaded: {{ formatDate(video()?.createdAt || '') }}</p>
              </ion-text>
            </ion-card-content>
          </ion-card>
        </div>
      </div>
    </ion-content>
  `,
  styles: [`
    .loading-container,
    .processing-container,
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 60vh;
      padding: 2rem;
    }

    .processing-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 1rem;
    }

    .processing-content ion-spinner {
      margin-bottom: 1rem;
    }

    .processing-content ion-progress-bar {
      width: 100%;
      margin: 1rem 0;
    }

    .error-container ion-card {
      width: 100%;
      max-width: 400px;
    }

    .error-message {
      font-style: italic;
      margin-top: 0.5rem;
    }

    .video-container {
      padding: 1rem;
    }

    .video-info {
      margin-top: 1rem;
    }

    plyr-player {
      width: 100%;
      border-radius: 8px;
      overflow: hidden;
    }

    /* Responsive video player */
    @media (min-width: 768px) {
      .video-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
      }
    }
  `]
})
export class VideoPlayerPage implements OnInit, OnDestroy {
  private readonly videoService = inject(VideoService);
  private readonly router = inject(Router);

  // Input from route parameter
  id = input.required<string>();

  // Signals for reactive state management
  video = signal<Video | null>(null);
  isLoading = signal(true);

  // Plyr configuration
  plyrSources: Plyr.Source[] = [];
  plyrOptions: Plyr.Options = {
    controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
    settings: ['quality', 'speed'],
    quality: {
      default: 720,
      options: [720, 480]
    }
  };

  private pollingSubscription?: Subscription;

  constructor() {
    addIcons({ arrowBack, refresh, alertCircle });
  }

  ngOnInit() {
    this.loadVideo();
  }

  ngOnDestroy() {
    this.stopPolling();
  }

  private loadVideo() {
    this.isLoading.set(true);
    this.videoService.getVideo(this.id()).subscribe({
      next: (video) => {
        this.video.set(video);
        this.isLoading.set(false);
        
        if (video.status === 'READY') {
          this.setupVideoPlayer(video);
        } else if (video.status === 'PROCESSING') {
          this.startPolling();
        }
      },
      error: (error) => {
        console.error('Error loading video:', error);
        this.isLoading.set(false);
      }
    });
  }

  private setupVideoPlayer(video: Video) {
    // Configure HLS sources for Plyr
    this.plyrSources = [
      {
        src: video.videoUrl,
        type: 'application/x-mpegURL', // HLS MIME type
        size: 720
      }
    ];
  }

  private startPolling() {
    this.stopPolling(); // Ensure no existing polling
    
    this.pollingSubscription = interval(5000) // Poll every 5 seconds
      .pipe(
        switchMap(() => this.videoService.getVideo(this.id())),
        takeWhile(video => video.status === 'PROCESSING', true)
      )
      .subscribe({
        next: (video) => {
          this.video.set(video);
          
          if (video.status === 'READY') {
            this.setupVideoPlayer(video);
            this.stopPolling();
          } else if (video.status === 'ERROR') {
            this.stopPolling();
          }
        },
        error: (error) => {
          console.error('Error polling video status:', error);
          this.stopPolling();
        }
      });
  }

  private stopPolling() {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = undefined;
    }
  }

  checkStatus() {
    this.loadVideo();
  }

  onPlayerInit(event: any) {
    console.log('Plyr player initialized:', event);
  }

  formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
}
