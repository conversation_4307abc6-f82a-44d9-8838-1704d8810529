/* You can add global styles to this file, and also import other style files */
@import 'vars';
@import "common";
@import "theme";

/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/* Plyr CSS for video player */
@import "plyr/dist/plyr.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
//@import "@ionic/angular/css/palettes/dark.system.css";


//@import '~material-design-icons/iconfont/material-icons.css';

html,body{
  margin: 0;
  min-height: 100%;
  min-width: 100%;
  padding: 0;
  font-family: "sourcesanspro", sans-serif;
  font-style: normal;
  font-weight: 100;
 // font-size: 62.5%;
}
//This SCSS code sets the box-sizing property to border-box for all elements,
// ensuring that padding and border are included in the element's total width and height.
* {
  box-sizing: border-box;
}

a{
  text-decoration: none;
}
body {
  max-width: 1200px;
  min-height: 100vh;
  background-color: var(--outer-color);
  display: flex;
  flex-direction: column;
  //font-size: 1rem;
}

// Custom styles for dollar icon alert
.cost-alert {
  .dollar-icons {
    display: inline-flex;
    align-items: center;
    
    svg {
      height: 18px;
      width: 18px;
      margin-right: 2px;
    }
  }
  
  .alert-radio-label {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
  }
}
