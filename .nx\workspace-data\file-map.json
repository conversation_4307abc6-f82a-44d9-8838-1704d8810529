{"version": "6.0", "nxVersion": "20.5.0", "pathMappings": {"@nutrition/auth": ["libs/auth/src/index.ts"], "@nutrition/camera-test": ["libs/playground/src/lib/playground/camera-test/src/index.ts"], "@nutrition/commons": ["libs/commons/src/index.ts"], "@nutrition/experts": ["libs/experts/src/index.ts"], "@nutrition/explorer": ["libs/explorer/src/index.ts"], "@nutrition/home": ["libs/home/<USER>/index.ts"], "@nutrition/journal": ["libs/journal/src/index.ts"], "@nutrition/plans": ["libs/plans/src/index.ts"], "@nutrition/playground": ["libs/playground/src/index.ts"], "@nutrition/shared": ["libs/shared/src/index.ts"], "@nutrition/shop": ["libs/shop/src/index.ts"], "@nutrition/user-profile": ["libs/user-profile/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"projectFileMap": {"journal": [{"file": "libs/journal/.eslintrc.json", "hash": "9628345067573567197"}, {"file": "libs/journal/README.md", "hash": "1504667544103032572"}, {"file": "libs/journal/jest.config.ts", "hash": "12541439617600883464"}, {"file": "libs/journal/project.json", "hash": "16706955066289133299"}, {"file": "libs/journal/src/index.ts", "hash": "4983097078843233039"}, {"file": "libs/journal/src/lib/journal/breakfast-modal/breakfast-modal.component.html", "hash": "1740935493838490298"}, {"file": "libs/journal/src/lib/journal/breakfast-modal/breakfast-modal.component.scss", "hash": "12532027548681458274"}, {"file": "libs/journal/src/lib/journal/breakfast-modal/breakfast-modal.component.ts", "hash": "12203781414688049379", "deps": ["npm:@angular/core", "npm:@ionic/angular", "npm:@angular/common"]}, {"file": "libs/journal/src/lib/journal/breakfast/journal-breakfast.component.html", "hash": "15075105526914365752"}, {"file": "libs/journal/src/lib/journal/breakfast/journal-breakfast.component.scss", "hash": "1064850519666162251"}, {"file": "libs/journal/src/lib/journal/breakfast/journal-breakfast.component.spec.ts", "hash": "756353386130442856", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/breakfast/journal-breakfast.component.ts", "hash": "4529900050181459080", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/journal/src/lib/journal/calendar/journal-calendar.component.html", "hash": "10249936111436396865"}, {"file": "libs/journal/src/lib/journal/calendar/journal-calendar.component.scss", "hash": "12698668273967373020"}, {"file": "libs/journal/src/lib/journal/calendar/journal-calendar.component.spec.ts", "hash": "10945787427172760499", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/calendar/journal-calendar.component.ts", "hash": "3941962105153411202", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms", "npm:ionicons"]}, {"file": "libs/journal/src/lib/journal/calories/journal-calories.component.html", "hash": "10800292510238610377"}, {"file": "libs/journal/src/lib/journal/calories/journal-calories.component.scss", "hash": "10942207103738725263"}, {"file": "libs/journal/src/lib/journal/calories/journal-calories.component.spec.ts", "hash": "9927362729093366345", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/calories/journal-calories.component.ts", "hash": "4379569164737079207", "deps": ["npm:@angular/core", "npm:@angular/common", "shared", "npm:@ionic/angular"]}, {"file": "libs/journal/src/lib/journal/calories/mental-icon/mental-icon.component.html", "hash": "16166458491339716403"}, {"file": "libs/journal/src/lib/journal/calories/mental-icon/mental-icon.component.scss", "hash": "6278003510439215712"}, {"file": "libs/journal/src/lib/journal/calories/mental-icon/mental-icon.component.spec.ts", "hash": "5401630994875090996", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/calories/mental-icon/mental-icon.component.ts", "hash": "14662728600674849345", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/journal/src/lib/journal/calories/nutrition-details/nutrition-details.component.html", "hash": "15260291385425260779"}, {"file": "libs/journal/src/lib/journal/calories/nutrition-details/nutrition-details.component.scss", "hash": "7126615837097077962"}, {"file": "libs/journal/src/lib/journal/calories/nutrition-details/nutrition-details.component.ts", "hash": "13795917610535790719", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/journal/src/lib/journal/calories/workout-icon/workout-icon.component.html", "hash": "115596502008749052"}, {"file": "libs/journal/src/lib/journal/calories/workout-icon/workout-icon.component.scss", "hash": "15691008721570183069"}, {"file": "libs/journal/src/lib/journal/calories/workout-icon/workout-icon.component.spec.ts", "hash": "1934340784978198254", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/calories/workout-icon/workout-icon.component.ts", "hash": "7592416612891413331", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-meditation/circle-meditation.component.html", "hash": "13114793739344958923"}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-meditation/circle-meditation.component.scss", "hash": "4174525247753885769"}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-meditation/circle-meditation.component.spec.ts", "hash": "12003804281019484098", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-meditation/circle-meditation.component.ts", "hash": "15516209165962478026", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/router"]}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-nutrition/circle-nutrition.component.html", "hash": "240537768098251006"}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-nutrition/circle-nutrition.component.scss", "hash": "10747238020593342521"}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-nutrition/circle-nutrition.component.spec.ts", "hash": "16951757537339173565", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-nutrition/circle-nutrition.component.ts", "hash": "8493425022509336742", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-selector.component.html", "hash": "11322000270068512731"}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-selector.component.scss", "hash": "13978480685621511504"}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-selector.component.spec.ts", "hash": "8660326176585158903", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-selector.component.ts", "hash": "6033002231840585548", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "shared"]}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-workout/circle-workout.component.html", "hash": "10645889394830466053"}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-workout/circle-workout.component.scss", "hash": "15805721717625795102"}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-workout/circle-workout.component.spec.ts", "hash": "5578951674589275273", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/circle-selector/circle-workout/circle-workout.component.ts", "hash": "16943785279168224937", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/router"]}, {"file": "libs/journal/src/lib/journal/dinner/journal-dinner.component.html", "hash": "15909296666722410116"}, {"file": "libs/journal/src/lib/journal/dinner/journal-dinner.component.scss", "hash": "3244421341483603138"}, {"file": "libs/journal/src/lib/journal/dinner/journal-dinner.component.spec.ts", "hash": "14828323835798406593", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/dinner/journal-dinner.component.ts", "hash": "16077534471615178800", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/journal/src/lib/journal/fasting-period/fasting-period.component.html", "hash": "1597792020418768704"}, {"file": "libs/journal/src/lib/journal/fasting-period/fasting-period.component.scss", "hash": "5885343218750217644"}, {"file": "libs/journal/src/lib/journal/fasting-period/fasting-period.component.spec.ts", "hash": "46244983588120021", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/fasting-period/fasting-period.component.ts", "hash": "2507736709794263463", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs", "npm:@ionic/angular", "npm:ionicons", "user-profile"]}, {"file": "libs/journal/src/lib/journal/fruits-veggies-suppl/fruits-veggies-suppl.component.html", "hash": "16516607621850358956"}, {"file": "libs/journal/src/lib/journal/fruits-veggies-suppl/fruits-veggies-suppl.component.scss", "hash": "6081769590534140884"}, {"file": "libs/journal/src/lib/journal/fruits-veggies-suppl/fruits-veggies-suppl.component.spec.ts", "hash": "1771253361539175999", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/fruits-veggies-suppl/fruits-veggies-suppl.component.ts", "hash": "8062905030299776420", "deps": ["npm:@angular/core", "npm:@angular/common", "shared", "npm:@ionic/angular"]}, {"file": "libs/journal/src/lib/journal/journal.component.html", "hash": "13000351690238627153"}, {"file": "libs/journal/src/lib/journal/journal.component.scss", "hash": "6057446976914833865"}, {"file": "libs/journal/src/lib/journal/journal.component.spec.ts", "hash": "7358425951785664773", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/journal.component.ts", "hash": "7152552835665802338", "deps": ["npm:@angular/core", "npm:@angular/common", "shared", "npm:@ionic/angular", "npm:@angular/router", "npm:ionicons", "user-profile"]}, {"file": "libs/journal/src/lib/journal/lunch/journal-lunch.component.html", "hash": "17495050551332172225"}, {"file": "libs/journal/src/lib/journal/lunch/journal-lunch.component.scss", "hash": "3244421341483603138"}, {"file": "libs/journal/src/lib/journal/lunch/journal-lunch.component.spec.ts", "hash": "12385864636702823228", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/lunch/journal-lunch.component.ts", "hash": "10218144750417874786", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/journal/src/lib/journal/meal-plan/dish-drag-drop.directive.ts", "hash": "7737677401015071569", "deps": ["npm:@angular/core", "npm:@ionic/angular", "npm:interactjs"]}, {"file": "libs/journal/src/lib/journal/meal-plan/dragDraopSwapDirective.directive.ts", "hash": "7424408184815062954", "deps": ["npm:@angular/core", "npm:@ionic/angular", "npm:interactjs"]}, {"file": "libs/journal/src/lib/journal/meal-plan/meal-drag-drop.directive.ts", "hash": "8638535119416960679", "deps": ["npm:@angular/core", "npm:@ionic/angular", "npm:interactjs"]}, {"file": "libs/journal/src/lib/journal/meal-plan/meal-plan-extended/meal-plan-extended.component.html", "hash": "236192587535062980"}, {"file": "libs/journal/src/lib/journal/meal-plan/meal-plan-extended/meal-plan-extended.component.scss", "hash": "5069973204151252900"}, {"file": "libs/journal/src/lib/journal/meal-plan/meal-plan-extended/meal-plan-extended.component.spec.ts", "hash": "6617978571522237474", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/meal-plan/meal-plan-extended/meal-plan-extended.component.ts", "hash": "563183363984317129", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/journal/src/lib/journal/meal-plan/meal-plan-short/meal-plan-short.component.html", "hash": "5875202764769169887"}, {"file": "libs/journal/src/lib/journal/meal-plan/meal-plan-short/meal-plan-short.component.scss", "hash": "3095916177034114413"}, {"file": "libs/journal/src/lib/journal/meal-plan/meal-plan-short/meal-plan-short.component.spec.ts", "hash": "7782397307717350547", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/meal-plan/meal-plan-short/meal-plan-short.component.ts", "hash": "8156150846164954751", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/journal/src/lib/journal/mental-activity/journal-mental-activity.component.html", "hash": "11871909662408699058"}, {"file": "libs/journal/src/lib/journal/mental-activity/journal-mental-activity.component.scss", "hash": "17903209455192068276"}, {"file": "libs/journal/src/lib/journal/mental-activity/journal-mental-activity.component.spec.ts", "hash": "10274408822784758715", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/mental-activity/journal-mental-activity.component.ts", "hash": "7684405578228162515", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/journal/src/lib/journal/overall-healthness-score/health-score-modal/health-score-modal.component.ts", "hash": "4948263440638890570", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:@ionic/angular"]}, {"file": "libs/journal/src/lib/journal/overall-healthness-score/journal-overall-healthness-score.component.html", "hash": "2471038422807899545"}, {"file": "libs/journal/src/lib/journal/overall-healthness-score/journal-overall-healthness-score.component.scss", "hash": "6584795914763768009"}, {"file": "libs/journal/src/lib/journal/overall-healthness-score/journal-overall-healthness-score.component.spec.ts", "hash": "13250361439463853278", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/overall-healthness-score/journal-overall-healthness-score.component.ts", "hash": "680372944664720137", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/journal/src/lib/journal/overall-healthness-score/overall-score-component/overall-score-component.component.html", "hash": "10230341015152136707"}, {"file": "libs/journal/src/lib/journal/overall-healthness-score/overall-score-component/overall-score-component.component.scss", "hash": "4958530060500178065"}, {"file": "libs/journal/src/lib/journal/overall-healthness-score/overall-score-component/overall-score-component.component.spec.ts", "hash": "5184070627955467672", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/overall-healthness-score/overall-score-component/overall-score-component.component.ts", "hash": "413463430071373464", "deps": ["npm:@angular/core", "npm:d3", "npm:@angular/common"]}, {"file": "libs/journal/src/lib/journal/portion-slider/portion-slider.component.html", "hash": "11113064581159141884"}, {"file": "libs/journal/src/lib/journal/portion-slider/portion-slider.component.scss", "hash": "12457339443108762084"}, {"file": "libs/journal/src/lib/journal/portion-slider/portion-slider.component.spec.ts", "hash": "1599370905313143833", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/portion-slider/portion-slider.component.ts", "hash": "412352320083664067", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms"]}, {"file": "libs/journal/src/lib/journal/snacks/journal-snacks.component.html", "hash": "9943829840711300523"}, {"file": "libs/journal/src/lib/journal/snacks/journal-snacks.component.scss", "hash": "3244421341483603138"}, {"file": "libs/journal/src/lib/journal/snacks/journal-snacks.component.spec.ts", "hash": "4743646035899763171", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/snacks/journal-snacks.component.ts", "hash": "15827264400096548495", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/journal/src/lib/journal/supplements/journal-supplements.component.html", "hash": "10302100429203090486"}, {"file": "libs/journal/src/lib/journal/supplements/journal-supplements.component.scss", "hash": "3244421341483603138"}, {"file": "libs/journal/src/lib/journal/supplements/journal-supplements.component.spec.ts", "hash": "12445150633147284820", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/supplements/journal-supplements.component.ts", "hash": "15103258151643394027", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/journal/src/lib/journal/vices/journal-vices.component.html", "hash": "6062221369062890373"}, {"file": "libs/journal/src/lib/journal/vices/journal-vices.component.scss", "hash": "16814602078384367164"}, {"file": "libs/journal/src/lib/journal/vices/journal-vices.component.spec.ts", "hash": "2548664358745496293", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/vices/journal-vices.component.ts", "hash": "8072732693344338887", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/journal/src/lib/journal/water-supplement/journal-water-supplement.component.html", "hash": "17355723284913743744"}, {"file": "libs/journal/src/lib/journal/water-supplement/journal-water-supplement.component.scss", "hash": "6081769590534140884"}, {"file": "libs/journal/src/lib/journal/water-supplement/journal-water-supplement.component.spec.ts", "hash": "18015053349587729428", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/water-supplement/journal-water-supplement.component.ts", "hash": "9626173002428516332", "deps": ["npm:@angular/core", "npm:@angular/common", "shared", "npm:@ionic/angular"]}, {"file": "libs/journal/src/lib/journal/workout/journal-workout.component.html", "hash": "10874868353979268122"}, {"file": "libs/journal/src/lib/journal/workout/journal-workout.component.scss", "hash": "9677025251676389552"}, {"file": "libs/journal/src/lib/journal/workout/journal-workout.component.spec.ts", "hash": "17080681187826497000", "deps": ["npm:@angular/core"]}, {"file": "libs/journal/src/lib/journal/workout/journal-workout.component.ts", "hash": "14267215489922950693", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/journal/src/lib/service/journal.service.ts", "hash": "14631150178410335569", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "libs/journal/src/lib/state/journal.model.ts", "hash": "16125833525649311430"}, {"file": "libs/journal/src/lib/state/journal.store.ts", "hash": "4521234075343031202", "deps": ["npm:@angular/core", "npm:@ngrx/operators", "npm:@ngrx/signals", "shared", "npm:rxjs"]}, {"file": "libs/journal/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/journal/tsconfig.json", "hash": "1083278824072282325"}, {"file": "libs/journal/tsconfig.lib.json", "hash": "5796668385415907511"}, {"file": "libs/journal/tsconfig.spec.json", "hash": "2927375835638640771"}], "auth": [{"file": "libs/auth/.eslintrc.json", "hash": "9628345067573567197"}, {"file": "libs/auth/README.md", "hash": "9370648284768385907"}, {"file": "libs/auth/jest.config.ts", "hash": "2454897979899206125"}, {"file": "libs/auth/project.json", "hash": "3687035062681619518"}, {"file": "libs/auth/src/assets/images/AdobeStock_244493306_Preview.jpeg", "hash": "382225385124238973"}, {"file": "libs/auth/src/assets/images/AdobeStock_249383578_Preview.jpeg", "hash": "4981491567792772149"}, {"file": "libs/auth/src/assets/images/AdobeStock_249383578_Preview_landscape.jpg", "hash": "12780991448301767797"}, {"file": "libs/auth/src/assets/images/AdobeStock_254451278_Preview.jpeg", "hash": "877729125406677524"}, {"file": "libs/auth/src/assets/images/facebook.png", "hash": "4253324473908274928"}, {"file": "libs/auth/src/assets/images/google.png", "hash": "3138261851630308343"}, {"file": "libs/auth/src/assets/images/heart-shape-by-various-vegetables-fruits.jpg", "hash": "6074193359180985823"}, {"file": "libs/auth/src/index.ts", "hash": "9569309497554618439"}, {"file": "libs/auth/src/lib/auth/auth.service.spec.ts", "hash": "6927655796975040699", "deps": ["npm:@angular/core"]}, {"file": "libs/auth/src/lib/auth/auth.service.ts", "hash": "4502188638631354183", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs", "shared", "npm:@angular/compiler-cli"]}, {"file": "libs/auth/src/lib/auth/guard/auth-guard.ts", "hash": "7989081022823457238", "deps": ["npm:rxjs", "npm:@angular/router", "npm:@angular/core", "shared", "npm:jwt-decode"]}, {"file": "libs/auth/src/lib/auth/login/login.component.html", "hash": "10990818674804151906"}, {"file": "libs/auth/src/lib/auth/login/login.component.scss", "hash": "17638656485834307611"}, {"file": "libs/auth/src/lib/auth/login/login.component.spec.ts", "hash": "15200146559264946551", "deps": ["npm:@angular/core"]}, {"file": "libs/auth/src/lib/auth/login/login.component.ts", "hash": "17346426696888860132", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:@angular/router", "npm:@ionic/angular", "npm:@ngx-translate/core", "shared"]}, {"file": "libs/auth/src/lib/auth/model/auth.model.ts", "hash": "10599352873356853105"}, {"file": "libs/auth/src/lib/auth/password-recovery/password-recovery.component.html", "hash": "6549393206236807012"}, {"file": "libs/auth/src/lib/auth/password-recovery/password-recovery.component.scss", "hash": "3244421341483603138"}, {"file": "libs/auth/src/lib/auth/password-recovery/password-recovery.component.spec.ts", "hash": "14848753589143307002", "deps": ["npm:@angular/core"]}, {"file": "libs/auth/src/lib/auth/password-recovery/password-recovery.component.ts", "hash": "10072991177882105094", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:@ionic/angular", "npm:@angular/router"]}, {"file": "libs/auth/src/lib/auth/register/register.component.html", "hash": "9269951035480045158"}, {"file": "libs/auth/src/lib/auth/register/register.component.scss", "hash": "4262044766458753971"}, {"file": "libs/auth/src/lib/auth/register/register.component.spec.ts", "hash": "16679337415367622742", "deps": ["npm:@angular/core", "npm:@ionic/angular"]}, {"file": "libs/auth/src/lib/auth/register/register.component.ts", "hash": "7752973770294803015", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:@ionic/angular"]}, {"file": "libs/auth/src/lib/auth/request-password-recovery/request-password-recovery.component.html", "hash": "11818683735378233419"}, {"file": "libs/auth/src/lib/auth/request-password-recovery/request-password-recovery.component.scss", "hash": "6405039455964394218"}, {"file": "libs/auth/src/lib/auth/request-password-recovery/request-password-recovery.component.spec.ts", "hash": "14187201973025818153", "deps": ["npm:@angular/core"]}, {"file": "libs/auth/src/lib/auth/request-password-recovery/request-password-recovery.component.ts", "hash": "9248138006805208913", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:@ionic/angular", "npm:@ngx-translate/core", "npm:@ngrx/signals"]}, {"file": "libs/auth/src/lib/auth/services/user.service.ts", "hash": "18025059806440866261", "deps": ["npm:@angular/core", "npm:rxjs", "npm:@angular/common"]}, {"file": "libs/auth/src/lib/auth/state/auth.store.ts", "hash": "17660300877738658197", "deps": ["npm:@ngrx/signals"]}, {"file": "libs/auth/src/lib/auth/tokenInterceptor.ts", "hash": "7191412927119018543", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs", "shared"]}, {"file": "libs/auth/src/lib/auth/validation/password-match.directive.ts", "hash": "1287571574493745069", "deps": ["npm:@angular/core", "npm:@angular/forms"]}, {"file": "libs/auth/src/test-setup.ts", "hash": "12935735732682149507", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/auth/tsconfig.json", "hash": "1083278824072282325"}, {"file": "libs/auth/tsconfig.lib.json", "hash": "5796668385415907511"}, {"file": "libs/auth/tsconfig.spec.json", "hash": "2927375835638640771"}], "plans": [{"file": "libs/plans/.eslintrc.json", "hash": "9628345067573567197"}, {"file": "libs/plans/README.md", "hash": "7169651611537827464"}, {"file": "libs/plans/jest.config.ts", "hash": "15625245967608200930"}, {"file": "libs/plans/project.json", "hash": "13559515064118792647"}, {"file": "libs/plans/src/index.ts", "hash": "2260432923815471494"}, {"file": "libs/plans/src/lib/plans/plans-food-dashboard/plans-food-dashboard.component.html", "hash": "14806708313368457552"}, {"file": "libs/plans/src/lib/plans/plans-food-dashboard/plans-food-dashboard.component.scss", "hash": "3244421341483603138"}, {"file": "libs/plans/src/lib/plans/plans-food-dashboard/plans-food-dashboard.component.spec.ts", "hash": "8133700800298898158", "deps": ["npm:@angular/core"]}, {"file": "libs/plans/src/lib/plans/plans-food-dashboard/plans-food-dashboard.component.ts", "hash": "16531816444899261926", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/plans/src/lib/plans/plans-mental-dashboard/plans-mental-dashboard.component.html", "hash": "674098131805575500"}, {"file": "libs/plans/src/lib/plans/plans-mental-dashboard/plans-mental-dashboard.component.scss", "hash": "3244421341483603138"}, {"file": "libs/plans/src/lib/plans/plans-mental-dashboard/plans-mental-dashboard.component.spec.ts", "hash": "4918097975735754402", "deps": ["npm:@angular/core"]}, {"file": "libs/plans/src/lib/plans/plans-mental-dashboard/plans-mental-dashboard.component.ts", "hash": "1305116261518170412", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/plans/src/lib/plans/plans-router-outlet/plans-router-outlet.component.html", "hash": "2552821310812562732"}, {"file": "libs/plans/src/lib/plans/plans-router-outlet/plans-router-outlet.component.scss", "hash": "3244421341483603138"}, {"file": "libs/plans/src/lib/plans/plans-router-outlet/plans-router-outlet.component.spec.ts", "hash": "3337526700848094234", "deps": ["npm:@angular/core"]}, {"file": "libs/plans/src/lib/plans/plans-router-outlet/plans-router-outlet.component.ts", "hash": "1560641429161385133", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/plans/src/lib/plans/plans-workout-dashboard/plans-workout-dashboard.component.html", "hash": "2621617487676899721"}, {"file": "libs/plans/src/lib/plans/plans-workout-dashboard/plans-workout-dashboard.component.scss", "hash": "3244421341483603138"}, {"file": "libs/plans/src/lib/plans/plans-workout-dashboard/plans-workout-dashboard.component.spec.ts", "hash": "13013713819876367620", "deps": ["npm:@angular/core"]}, {"file": "libs/plans/src/lib/plans/plans-workout-dashboard/plans-workout-dashboard.component.ts", "hash": "15521515378945558775", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/plans/src/lib/plans/plans.component.html", "hash": "9667118350405931496"}, {"file": "libs/plans/src/lib/plans/plans.component.scss", "hash": "7633474194515351414"}, {"file": "libs/plans/src/lib/plans/plans.component.spec.ts", "hash": "11412146578737386870", "deps": ["npm:@angular/core"]}, {"file": "libs/plans/src/lib/plans/plans.component.ts", "hash": "3679803650438983427", "deps": ["npm:@angular/core", "npm:@angular/common", "shared", "npm:@ionic/angular", "npm:@angular/router"]}, {"file": "libs/plans/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/plans/tsconfig.json", "hash": "1083278824072282325"}, {"file": "libs/plans/tsconfig.lib.json", "hash": "5796668385415907511"}, {"file": "libs/plans/tsconfig.spec.json", "hash": "2927375835638640771"}], "experts": [{"file": "libs/experts/.eslintrc.json", "hash": "9628345067573567197"}, {"file": "libs/experts/README.md", "hash": "5290307021589608927"}, {"file": "libs/experts/jest.config.ts", "hash": "3315070069593812600"}, {"file": "libs/experts/project.json", "hash": "16354856978474327585"}, {"file": "libs/experts/src/index.ts", "hash": "7789177396773647009"}, {"file": "libs/experts/src/lib/experts/experts.component.css", "hash": "3244421341483603138"}, {"file": "libs/experts/src/lib/experts/experts.component.html", "hash": "11632476256565783127"}, {"file": "libs/experts/src/lib/experts/experts.component.spec.ts", "hash": "419480746375362715", "deps": ["npm:@angular/core"]}, {"file": "libs/experts/src/lib/experts/experts.component.ts", "hash": "14853177631350199948", "deps": ["npm:@angular/core", "npm:@angular/common", "shared"]}, {"file": "libs/experts/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/experts/tsconfig.json", "hash": "1083278824072282325"}, {"file": "libs/experts/tsconfig.lib.json", "hash": "5796668385415907511"}, {"file": "libs/experts/tsconfig.spec.json", "hash": "2927375835638640771"}], "nutrition-admin": [{"file": "apps/nutrition-admin/.eslintrc.json", "hash": "3322449398258820698"}, {"file": "apps/nutrition-admin/jest.config.ts", "hash": "8763495696946893017"}, {"file": "apps/nutrition-admin/project.json", "hash": "363975867469600334"}, {"file": "apps/nutrition-admin/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/nutrition-admin/src/app/app.component.html", "hash": "5654460193981998828"}, {"file": "apps/nutrition-admin/src/app/app.component.scss", "hash": "3244421341483603138"}, {"file": "apps/nutrition-admin/src/app/app.component.spec.ts", "hash": "10877286432849253488", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/nutrition-admin/src/app/app.component.ts", "hash": "438503760057345594", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/nutrition-admin/src/app/app.config.ts", "hash": "6137170128205633588", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/nutrition-admin/src/app/app.routes.ts", "hash": "6185150965714414910", "deps": ["npm:@angular/router"]}, {"file": "apps/nutrition-admin/src/app/nx-welcome.component.ts", "hash": "7474338468913782841", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/nutrition-admin/src/index.html", "hash": "7843269792030256404"}, {"file": "apps/nutrition-admin/src/main.ts", "hash": "16635586463787978962", "deps": ["npm:@angular/platform-browser"]}, {"file": "apps/nutrition-admin/src/styles.scss", "hash": "5195668842064076916"}, {"file": "apps/nutrition-admin/src/test-setup.ts", "hash": "12935735732682149507", "deps": ["npm:jest-preset-angular"]}, {"file": "apps/nutrition-admin/tsconfig.app.json", "hash": "8365644956319658761"}, {"file": "apps/nutrition-admin/tsconfig.editor.json", "hash": "1055794200401717100"}, {"file": "apps/nutrition-admin/tsconfig.json", "hash": "6424220500541951295"}, {"file": "apps/nutrition-admin/tsconfig.spec.json", "hash": "2927375835638640771"}], "camera-test": [{"file": "libs/playground/src/lib/playground/camera-test/.eslintrc.json", "hash": "9836333035461179736"}, {"file": "libs/playground/src/lib/playground/camera-test/README.md", "hash": "2498935910049180718"}, {"file": "libs/playground/src/lib/playground/camera-test/jest.config.ts", "hash": "12914829293250483015"}, {"file": "libs/playground/src/lib/playground/camera-test/project.json", "hash": "13846778411381862625"}, {"file": "libs/playground/src/lib/playground/camera-test/src/index.ts", "hash": "11312025238562224381"}, {"file": "libs/playground/src/lib/playground/camera-test/src/lib/camera-test/camera-test.component.css", "hash": "3244421341483603138"}, {"file": "libs/playground/src/lib/playground/camera-test/src/lib/camera-test/camera-test.component.html", "hash": "9111212559819069207"}, {"file": "libs/playground/src/lib/playground/camera-test/src/lib/camera-test/camera-test.component.spec.ts", "hash": "16651606166644489338", "deps": ["npm:@angular/core"]}, {"file": "libs/playground/src/lib/playground/camera-test/src/lib/camera-test/camera-test.component.ts", "hash": "18443969170858760394", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@capacitor/camera", "npm:@capacitor/filesystem", "npm:@ionic/angular"]}, {"file": "libs/playground/src/lib/playground/camera-test/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/playground/src/lib/playground/camera-test/tsconfig.json", "hash": "12117135597590931651"}, {"file": "libs/playground/src/lib/playground/camera-test/tsconfig.lib.json", "hash": "3979047174660541195"}, {"file": "libs/playground/src/lib/playground/camera-test/tsconfig.spec.json", "hash": "11449423562439716344"}], "nutrition": [{"file": "apps/nutrition/.eslintrc.json", "hash": "9841770291056711405"}, {"file": "apps/nutrition/assets/bipolar_18998506.svg", "hash": "17894606775980835947"}, {"file": "apps/nutrition/assets/glass-water_3717054.png", "hash": "11720276087633543107"}, {"file": "apps/nutrition/assets/gym-svgrepo-com.svg", "hash": "3681451179759370744"}, {"file": "apps/nutrition/ionic.config.json", "hash": "11709504145606813656"}, {"file": "apps/nutrition/jest.config.ts", "hash": "13611858329214204336"}, {"file": "apps/nutrition/project.json", "hash": "1214300766738975105"}, {"file": "apps/nutrition/public/Female_Body.svg", "hash": "6002385269244629224"}, {"file": "apps/nutrition/public/Male_Body.svg", "hash": "611049162195927247"}, {"file": "apps/nutrition/public/cusine/chinese/<EMAIL>", "hash": "993218239336449115"}, {"file": "apps/nutrition/public/cusine/chinese/<EMAIL>", "hash": "18271643407120865923"}, {"file": "apps/nutrition/public/cusine/french/<EMAIL>", "hash": "17296512526862141357"}, {"file": "apps/nutrition/public/cusine/french/<EMAIL>", "hash": "6463459200247999580"}, {"file": "apps/nutrition/public/cusine/greek/<EMAIL>", "hash": "6486222350206246042"}, {"file": "apps/nutrition/public/cusine/greek/<EMAIL>", "hash": "1671824601109736946"}, {"file": "apps/nutrition/public/cusine/hungarian/<EMAIL>", "hash": "12922627640858371694"}, {"file": "apps/nutrition/public/cusine/hungarian/<EMAIL>", "hash": "2618165559646811868"}, {"file": "apps/nutrition/public/cusine/indian/<EMAIL>", "hash": "7222856429036672055"}, {"file": "apps/nutrition/public/cusine/indian/<EMAIL>", "hash": "18199505232647491358"}, {"file": "apps/nutrition/public/cusine/italian/<EMAIL>", "hash": "13478840768703296051"}, {"file": "apps/nutrition/public/cusine/italian/<EMAIL>", "hash": "7224993147213834479"}, {"file": "apps/nutrition/public/cusine/japanese/<EMAIL>", "hash": "7543523671800866981"}, {"file": "apps/nutrition/public/cusine/japanese/<EMAIL>", "hash": "15952283974794025033"}, {"file": "apps/nutrition/public/cusine/korean/<EMAIL>", "hash": "9469432773998917764"}, {"file": "apps/nutrition/public/cusine/korean/<EMAIL>", "hash": "13154047317558468853"}, {"file": "apps/nutrition/public/cusine/mexican/<EMAIL>", "hash": "9549794638363547332"}, {"file": "apps/nutrition/public/cusine/mexican/<EMAIL>", "hash": "8482077678503734748"}, {"file": "apps/nutrition/public/cusine/middleeastern/<EMAIL>", "hash": "10023060372841678069"}, {"file": "apps/nutrition/public/cusine/middleeastern/<EMAIL>", "hash": "13655141784595311053"}, {"file": "apps/nutrition/public/cusine/romanian/<EMAIL>", "hash": "2599981617231379318"}, {"file": "apps/nutrition/public/cusine/romanian/<EMAIL>", "hash": "8448974131908370083"}, {"file": "apps/nutrition/public/cusine/scandinavian/<EMAIL>", "hash": "7284683652387516082"}, {"file": "apps/nutrition/public/cusine/scandinavian/<EMAIL>", "hash": "15824217670422739187"}, {"file": "apps/nutrition/public/cusine/slavic/<EMAIL>", "hash": "2149624793943454309"}, {"file": "apps/nutrition/public/cusine/slavic/<EMAIL>", "hash": "6866425118872802831"}, {"file": "apps/nutrition/public/cusine/spanish/<EMAIL>", "hash": "12226473071667180567"}, {"file": "apps/nutrition/public/cusine/spanish/<EMAIL>", "hash": "994953135466417990"}, {"file": "apps/nutrition/public/cusine/thai/<EMAIL>", "hash": "1592781841988360290"}, {"file": "apps/nutrition/public/cusine/thai/<EMAIL>", "hash": "17479468635090563110"}, {"file": "apps/nutrition/public/dashboard/Rectangle <EMAIL>", "hash": "11270652538631166431"}, {"file": "apps/nutrition/public/dashboard/Rectangle <EMAIL>", "hash": "16573580290177260969"}, {"file": "apps/nutrition/public/dashboard/Rectangle <EMAIL>", "hash": "14423168515003555866"}, {"file": "apps/nutrition/public/dashboard/apple_2437767.png", "hash": "8746992577997491116"}, {"file": "apps/nutrition/public/dashboard/breakfast/image.png", "hash": "10583354784897708960"}, {"file": "apps/nutrition/public/dashboard/breakfast/image2.png", "hash": "1813548878855748404"}, {"file": "apps/nutrition/public/dashboard/dinner/dinner-placholder.jpg", "hash": "13276943310735583389"}, {"file": "apps/nutrition/public/dashboard/dinner/dinner.png", "hash": "4049269936572837892"}, {"file": "apps/nutrition/public/dashboard/eat-food-healthy-life-svgrepo-com.svg", "hash": "9997853837595195817"}, {"file": "apps/nutrition/public/dashboard/food_11901708.png", "hash": "301029795470056930"}, {"file": "apps/nutrition/public/dashboard/fruit_10580496.png", "hash": "9331993090075285154"}, {"file": "apps/nutrition/public/dashboard/glass-water_3717054.png", "hash": "11720276087633543107"}, {"file": "apps/nutrition/public/dashboard/gymnast-muscular-arm-outline-with-a-timer-svgrepo-com.svg", "hash": "12879661215095238736"}, {"file": "apps/nutrition/public/dashboard/lunch/lunch.png", "hash": "10975606293974473820"}, {"file": "apps/nutrition/public/dashboard/meditation/meditation-placeholder.jpg", "hash": "15460606582795861988"}, {"file": "apps/nutrition/public/dashboard/meditation/meditation.png", "hash": "13108153716780879954"}, {"file": "apps/nutrition/public/dashboard/meditation/reading.png", "hash": "7547424305527521442"}, {"file": "apps/nutrition/public/dashboard/snack/image.png", "hash": "12203936449439767944"}, {"file": "apps/nutrition/public/dashboard/workout/w1.png", "hash": "13427230381606381122"}, {"file": "apps/nutrition/public/dashboard/workout/w2.png", "hash": "4092197311724190343"}, {"file": "apps/nutrition/public/dashboard/workout/workout-placeholder.jpg", "hash": "11559493234191439760"}, {"file": "apps/nutrition/public/dashboard/yoga-posture-svgrepo-com.svg", "hash": "5043628088777611135"}, {"file": "apps/nutrition/public/explorer/meditation/ambient.mp3", "hash": "1323478999454488491"}, {"file": "apps/nutrition/public/explorer/workouts/a1.png", "hash": "7541728811189968270"}, {"file": "apps/nutrition/public/explorer/workouts/a2.png", "hash": "14260790025458362999"}, {"file": "apps/nutrition/public/explorer/workouts/a3.png", "hash": "7964668613883007762"}, {"file": "apps/nutrition/public/explorer/workouts/a4.png", "hash": "2755698938499355621"}, {"file": "apps/nutrition/public/explorer/workouts/arms.jpg", "hash": "12275873619802320757"}, {"file": "apps/nutrition/public/explorer/workouts/back.jpg", "hash": "8007468101170363083"}, {"file": "apps/nutrition/public/explorer/workouts/chest.jpg", "hash": "5549584290173641863"}, {"file": "apps/nutrition/public/explorer/workouts/legs.jpg", "hash": "17371594576004942060"}, {"file": "apps/nutrition/public/explorer/workouts/shoulders.jpg", "hash": "16956208389244890951"}, {"file": "apps/nutrition/public/explorer/workouts/stretching.jpg", "hash": "15597232396396173627"}, {"file": "apps/nutrition/public/explorer/workouts/w1.png", "hash": "17037042992486930010"}, {"file": "apps/nutrition/public/explorer/workouts/w2.png", "hash": "9912968649579923561"}, {"file": "apps/nutrition/public/explorer/workouts/w3.png", "hash": "8423930522472337991"}, {"file": "apps/nutrition/public/facebook.png", "hash": "4253324473908274928"}, {"file": "apps/nutrition/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/nutrition/public/freepik__background__57850.png", "hash": "9244079334497271432"}, {"file": "apps/nutrition/public/glass-water_3717054.png", "hash": "11720276087633543107"}, {"file": "apps/nutrition/public/google.png", "hash": "3138261851630308343"}, {"file": "apps/nutrition/public/i18n/ro.json", "hash": "4150604557318037770"}, {"file": "apps/nutrition/public/profile/carbs.png", "hash": "9110907010231787105"}, {"file": "apps/nutrition/public/profile/fats.png", "hash": "12555272029350476597"}, {"file": "apps/nutrition/public/profile/hydration.png", "hash": "12070743205641383450"}, {"file": "apps/nutrition/public/profile/mental.png", "hash": "5942185871927122196"}, {"file": "apps/nutrition/public/profile/proteins.png", "hash": "2273982094379494278"}, {"file": "apps/nutrition/public/profile/vitamins.png", "hash": "12531336018957131706"}, {"file": "apps/nutrition/public/profile/workout.png", "hash": "10975779000029879163"}, {"file": "apps/nutrition/public/profile/zahar.png", "hash": "11068388767059340715"}, {"file": "apps/nutrition/public/questionnaire/assortment-of-fruits.jpg", "hash": "304353316699548289"}, {"file": "apps/nutrition/public/questionnaire/cheese.png", "hash": "9694669254305972208"}, {"file": "apps/nutrition/public/questionnaire/chicken.png", "hash": "5731104223169688269"}, {"file": "apps/nutrition/public/questionnaire/egg.png", "hash": "11636433800736317170"}, {"file": "apps/nutrition/public/questionnaire/fish.png", "hash": "11852330785646165795"}, {"file": "apps/nutrition/public/questionnaire/questionnaire.jpg", "hash": "16692951581998197371"}, {"file": "apps/nutrition/public/questionnaire/steak.png", "hash": "16902519437495140854"}, {"file": "apps/nutrition/public/shake-hands.png", "hash": "4206608587151033079"}, {"file": "apps/nutrition/public/tools/aragaz.jpg", "hash": "8126961670448002819"}, {"file": "apps/nutrition/public/tools/blender.jpg", "hash": "11361102118332728401"}, {"file": "apps/nutrition/public/tools/cuptor.jpg", "hash": "13589568933007677084"}, {"file": "apps/nutrition/public/tools/forma-copt.jpg", "hash": "8818156574117867274"}, {"file": "apps/nutrition/public/tools/grill.jpg", "hash": "7159851054639462028"}, {"file": "apps/nutrition/public/tools/microunde.jpg", "hash": "16446096746543389018"}, {"file": "apps/nutrition/public/tools/mixer.jpg", "hash": "3386421270695065591"}, {"file": "apps/nutrition/public/tools/multicooker.jpg", "hash": "17376091037666477151"}, {"file": "apps/nutrition/public/tools/oala.jpg", "hash": "14423135610377658629"}, {"file": "apps/nutrition/public/tools/razatoare.jpg", "hash": "1906355432648799720"}, {"file": "apps/nutrition/public/tools/spumator.jpg", "hash": "2230380553366802637"}, {"file": "apps/nutrition/public/tools/sushi.jpg", "hash": "7169772915484289221"}, {"file": "apps/nutrition/public/tools/tava-p.jpg", "hash": "5644413240200685498"}, {"file": "apps/nutrition/public/tools/tava.jpg", "hash": "13946508715895360999"}, {"file": "apps/nutrition/public/tools/tel.jpg", "hash": "4609359621858033231"}, {"file": "apps/nutrition/public/tools/termometru.jpg", "hash": "17221093576135113356"}, {"file": "apps/nutrition/public/tools/tigaie-grill.jpg", "hash": "16257165596967715173"}, {"file": "apps/nutrition/public/tools/tigaie.jpg", "hash": "1965094198895539132"}, {"file": "apps/nutrition/public/tools/torta.jpg", "hash": "10895764728948801514"}, {"file": "apps/nutrition/public/tools/wok.jpg", "hash": "11252207345241359819"}, {"file": "apps/nutrition/src/app/app.component.html", "hash": "3710849332730438648"}, {"file": "apps/nutrition/src/app/app.component.scss", "hash": "9626923121757695497"}, {"file": "apps/nutrition/src/app/app.component.spec.ts", "hash": "11528557266979074035", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/nutrition/src/app/app.component.ts", "hash": "6706129580457429916", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:@ionic/angular", "npm:@ngx-translate/core"]}, {"file": "apps/nutrition/src/app/app.config.ts", "hash": "6977765336657837937", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:@angular/common", "npm:@ionic/angular", "npm:@ngx-translate/core", "npm:@ngx-translate/http-loader", "auth", "shared", "npm:@ionic/storage-angular", "npm:@ionic/storage", "npm:@angular/platform-browser"]}, {"file": "apps/nutrition/src/app/app.routes.ts", "hash": "9188265138439152554", "deps": ["npm:@angular/router", "auth", "shared", ["user-profile", "dynamic"], ["playground", "dynamic"], ["camera-test", "dynamic"], ["journal", "dynamic"], ["plans", "dynamic"], ["explorer", "dynamic"], ["experts", "dynamic"], ["shop", "dynamic"]]}, {"file": "apps/nutrition/src/app/features/products/model/product.ts", "hash": "9596067779872103500"}, {"file": "apps/nutrition/src/app/features/products/model/productDetails.ts", "hash": "5435305269139750525"}, {"file": "apps/nutrition/src/app/features/products/nutrients-table/nutrients-table.component.html", "hash": "4828604672973640033"}, {"file": "apps/nutrition/src/app/features/products/nutrients-table/nutrients-table.component.scss", "hash": "11600975511424574126"}, {"file": "apps/nutrition/src/app/features/products/nutrients-table/nutrients-table.component.spec.ts", "hash": "13233331063701885212", "deps": ["npm:@angular/core"]}, {"file": "apps/nutrition/src/app/features/products/nutrients-table/nutrients-table.component.ts", "hash": "6193682788561566638", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/nutrition/src/app/features/products/product-dashboard/product-dashboard.component.html", "hash": "17957020996192217507"}, {"file": "apps/nutrition/src/app/features/products/product-dashboard/product-dashboard.component.scss", "hash": "1952087559022746925"}, {"file": "apps/nutrition/src/app/features/products/product-dashboard/product-dashboard.component.spec.ts", "hash": "9872727463792916006", "deps": ["npm:@angular/core"]}, {"file": "apps/nutrition/src/app/features/products/product-dashboard/product-dashboard.component.ts", "hash": "11384492564935873954", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/router", "shared"]}, {"file": "apps/nutrition/src/app/features/products/product-details/product-details.component.html", "hash": "9721131694844842244"}, {"file": "apps/nutrition/src/app/features/products/product-details/product-details.component.scss", "hash": "3244421341483603138"}, {"file": "apps/nutrition/src/app/features/products/product-details/product-details.component.spec.ts", "hash": "14503164114245751034", "deps": ["npm:@angular/core"]}, {"file": "apps/nutrition/src/app/features/products/product-details/product-details.component.ts", "hash": "10998602563150668149", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router", "npm:rxjs", "shared"]}, {"file": "apps/nutrition/src/app/features/products/product-list/product-list-item/product-list-item.component.html", "hash": "18276971630464185796"}, {"file": "apps/nutrition/src/app/features/products/product-list/product-list-item/product-list-item.component.scss", "hash": "7920093363244312846"}, {"file": "apps/nutrition/src/app/features/products/product-list/product-list-item/product-list-item.component.spec.ts", "hash": "11600446587289646109", "deps": ["npm:@angular/core"]}, {"file": "apps/nutrition/src/app/features/products/product-list/product-list-item/product-list-item.component.ts", "hash": "1873919004299236913", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons", "npm:@angular/router"]}, {"file": "apps/nutrition/src/app/features/products/product-list/product-list.component.html", "hash": "2123873554008250322"}, {"file": "apps/nutrition/src/app/features/products/product-list/product-list.component.scss", "hash": "6635540941681630727"}, {"file": "apps/nutrition/src/app/features/products/product-list/product-list.component.spec.ts", "hash": "17959423433581110999", "deps": ["npm:@angular/core"]}, {"file": "apps/nutrition/src/app/features/products/product-list/product-list.component.ts", "hash": "8034008125259547427", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router", "npm:@ionic/angular", "shared"]}, {"file": "apps/nutrition/src/app/features/products/product.service.ts", "hash": "8004458369162013732", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs"]}, {"file": "apps/nutrition/src/app/features/products/products-compare/products-compare.component.html", "hash": "8948968186724483609"}, {"file": "apps/nutrition/src/app/features/products/products-compare/products-compare.component.scss", "hash": "10296611512041126288"}, {"file": "apps/nutrition/src/app/features/products/products-compare/products-compare.component.spec.ts", "hash": "8911322446868347981", "deps": ["npm:@angular/core"]}, {"file": "apps/nutrition/src/app/features/products/products-compare/products-compare.component.ts", "hash": "16960673190373149374", "deps": ["npm:@angular/core", "npm:@angular/common", "shared", "npm:rxjs", "npm:@angular/router", "npm:@ionic/angular"]}, {"file": "apps/nutrition/src/app/features/products/shared/products.store.ts", "hash": "14212289342058864281", "deps": ["npm:@ngrx/signals", "npm:@angular/animations", "npm:@angular/core"]}, {"file": "apps/nutrition/src/app/nx-welcome.component.ts", "hash": "5829271705085705582", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/nutrition/src/app/shared/appConfig.ts", "hash": "13696768975301156204"}, {"file": "apps/nutrition/src/app/shared/customNumber.pipe.ts", "hash": "567310865402964524", "deps": ["npm:@angular/core"]}, {"file": "apps/nutrition/src/app/shared/layout/main-layout/main-layout.component.html", "hash": "13328831166700301029"}, {"file": "apps/nutrition/src/app/shared/layout/main-layout/main-layout.component.scss", "hash": "4701553201481980449"}, {"file": "apps/nutrition/src/app/shared/layout/main-layout/main-layout.component.spec.ts", "hash": "8331098657017191012", "deps": ["npm:@angular/core"]}, {"file": "apps/nutrition/src/app/shared/layout/main-layout/main-layout.component.ts", "hash": "9421942290434337517", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "apps/nutrition/src/app/shared/stagewise.service.ts", "hash": "4854050438960927120", "deps": ["npm:@angular/core", "npm:@angular/common", ["npm:@stagewise/toolbar", "dynamic"]]}, {"file": "apps/nutrition/src/environments/environment.prod.ts", "hash": "10369965010669497467"}, {"file": "apps/nutrition/src/environments/environment.ts", "hash": "6790449082906928315"}, {"file": "apps/nutrition/src/index.html", "hash": "14629820062761228799"}, {"file": "apps/nutrition/src/main.ts", "hash": "8981530484107811853", "deps": ["npm:@angular/platform-browser", "npm:ionicons", "npm:swiper"]}, {"file": "apps/nutrition/src/style/common.scss", "hash": "7101340269487583308"}, {"file": "apps/nutrition/src/style/commons/chips.scss", "hash": "16220643789246928207"}, {"file": "apps/nutrition/src/style/commons/common-cards.scss", "hash": "1889207500905970078"}, {"file": "apps/nutrition/src/style/commons/list.scss", "hash": "387113405386459775"}, {"file": "apps/nutrition/src/style/commons/questions-lifestyle.scss", "hash": "6186683704395697525"}, {"file": "apps/nutrition/src/style/global.scss", "hash": "9010284628142451505"}, {"file": "apps/nutrition/src/style/styles.scss", "hash": "13777168758480398036"}, {"file": "apps/nutrition/src/style/theme.scss", "hash": "15578158262839921872"}, {"file": "apps/nutrition/src/style/vars.scss", "hash": "17837696940443635455"}, {"file": "apps/nutrition/src/test-setup.ts", "hash": "10765977267162271731", "deps": ["npm:jest-preset-angular"]}, {"file": "apps/nutrition/tsconfig.app.json", "hash": "9889529694974030583"}, {"file": "apps/nutrition/tsconfig.editor.json", "hash": "17575078801745175164"}, {"file": "apps/nutrition/tsconfig.json", "hash": "6366082015055176595"}, {"file": "apps/nutrition/tsconfig.spec.json", "hash": "1930986853382524452"}], "home": [{"file": "libs/home/<USER>", "hash": "9628345067573567197"}, {"file": "libs/home/<USER>", "hash": "11867757256229368821"}, {"file": "libs/home/<USER>", "hash": "9767745324320386734"}, {"file": "libs/home/<USER>", "hash": "12166338702754831704"}, {"file": "libs/home/<USER>/index.ts", "hash": "16342128905101411898"}, {"file": "libs/home/<USER>/lib/home/<USER>", "hash": "3244421341483603138"}, {"file": "libs/home/<USER>/lib/home/<USER>", "hash": "1590715050456772810"}, {"file": "libs/home/<USER>/lib/home/<USER>", "hash": "2620641819347346630", "deps": ["npm:@angular/core"]}, {"file": "libs/home/<USER>/lib/home/<USER>", "hash": "6483194751856051747", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/home/<USER>/test-setup.ts", "hash": "12935735732682149507", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/home/<USER>", "hash": "15487755565970508470"}, {"file": "libs/home/<USER>", "hash": "5796668385415907511"}, {"file": "libs/home/<USER>", "hash": "2927375835638640771"}], "shared": [{"file": "libs/shared/.eslintrc.json", "hash": "9628345067573567197"}, {"file": "libs/shared/README.md", "hash": "18321119346861532697"}, {"file": "libs/shared/jest.config.ts", "hash": "14509362061543395623"}, {"file": "libs/shared/project.json", "hash": "4315085708400201773"}, {"file": "libs/shared/src/index.ts", "hash": "8416288270424147206"}, {"file": "libs/shared/src/lib/components/question-list/question-list/question-list.component.html", "hash": "16759402027271900316"}, {"file": "libs/shared/src/lib/components/question-list/question-list/question-list.component.scss", "hash": "6711181144499901364"}, {"file": "libs/shared/src/lib/components/question-list/question-list/question-list.component.ts", "hash": "16615829256852254611", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms"]}, {"file": "libs/shared/src/lib/fake-states/real-world/article.store.ts", "hash": "16801461791539745933"}, {"file": "libs/shared/src/lib/fake-states/real-world/articles-list.store.ts", "hash": "5958613149723906823"}, {"file": "libs/shared/src/lib/fake-states/real-world/auth.store.ts", "hash": "13800468478891165783"}, {"file": "libs/shared/src/lib/fake-states/real-world/profile.store.ts", "hash": "4748526758960086690"}, {"file": "libs/shared/src/lib/fake-states/store/todos.model.ts", "hash": "6510577368541381609"}, {"file": "libs/shared/src/lib/fake-states/store/todos.store.ts", "hash": "15584862234780184178", "deps": ["npm:@ngrx/signals", "npm:@angular/core", "npm:@angular/common", "npm:rxjs"]}, {"file": "libs/shared/src/lib/service/accessibility-focus.service.ts", "hash": "13415836897965962549", "deps": ["npm:@angular/core", "npm:@ionic/angular"]}, {"file": "libs/shared/src/lib/shared/app.constants.ts", "hash": "4243393951826729051"}, {"file": "libs/shared/src/lib/shared/forms/forms-errors.store.ts", "hash": "1641837191282123829", "deps": ["npm:@angular/core", "npm:@ngrx/signals"]}, {"file": "libs/shared/src/lib/shared/forms/input-errors/error-mapper-pipe.ts", "hash": "10411005723239407188", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/lib/shared/forms/input-errors/error-messages.ts", "hash": "10035099404122558005", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/lib/shared/forms/input-errors/input-errors.component.html", "hash": "17307704417750575047"}, {"file": "libs/shared/src/lib/shared/forms/input-errors/input-errors.component.ts", "hash": "4676727251871787252", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms"]}, {"file": "libs/shared/src/lib/shared/forms/input-errors/is-error-visible.directive.ts", "hash": "16196255586079690566", "deps": ["npm:@angular/core", "npm:@angular/forms"]}, {"file": "libs/shared/src/lib/shared/forms/list-errors/list-errors.component.html", "hash": "113447519490206700"}, {"file": "libs/shared/src/lib/shared/forms/list-errors/list-errors.component.ts", "hash": "3416693833018002326", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/lib/shared/guards/composite-guard.guard.ts", "hash": "1935075891557611551", "deps": ["npm:@angular/router", "npm:@angular/core", "npm:rxjs", "npm:@ionic/angular"]}, {"file": "libs/shared/src/lib/shared/http/api-url.token.ts", "hash": "12119500561462836442", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/lib/shared/http/api.service.ts", "hash": "12033694625230012423", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs"]}, {"file": "libs/shared/src/lib/shared/services/local-storage.service.ts", "hash": "18299195258868186480", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/lib/signal-state/call-state.feature.ts", "hash": "1915279126216256725", "deps": ["npm:@angular/core", "npm:@ngrx/signals"]}, {"file": "libs/shared/src/lib/signal-state/global-state.ts", "hash": "208359825068656067", "deps": ["npm:@ngrx/signals"]}, {"file": "libs/shared/src/lib/storage/storage.service.ts", "hash": "7390862595635042291", "deps": ["npm:@angular/core", "npm:@ionic/storage-angular"]}, {"file": "libs/shared/src/test-setup.ts", "hash": "12935735732682149507", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/shared/src/ui/circle-wrapper/circle-wrapper.component.html", "hash": "18046952246128085579"}, {"file": "libs/shared/src/ui/circle-wrapper/circle-wrapper.component.scss", "hash": "13823283219375654037"}, {"file": "libs/shared/src/ui/circle-wrapper/circle-wrapper.component.ts", "hash": "6339842224739263107", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/animations", "npm:d3"]}, {"file": "libs/shared/src/ui/custom-svg-icon/custom-svg-icon.component.ts", "hash": "1474205527829153503", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/platform-browser"]}, {"file": "libs/shared/src/ui/error-message/error-message.component.html", "hash": "10799510726626060680"}, {"file": "libs/shared/src/ui/error-message/error-message.component.scss", "hash": "6272960754634171270"}, {"file": "libs/shared/src/ui/error-message/error-message.component.spec.ts", "hash": "16810575869071207045", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/ui/error-message/error-message.component.ts", "hash": "4708986504613306996", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons", "npm:swiper"]}, {"file": "libs/shared/src/ui/error-message/error-message.store.ts", "hash": "401808626542194368", "deps": ["npm:@ngrx/signals", "npm:@angular/core"]}, {"file": "libs/shared/src/ui/form-date-picker/form-date-picker.component.html", "hash": "4002861661856974336"}, {"file": "libs/shared/src/ui/form-date-picker/form-date-picker.component.scss", "hash": "12656118149463469442"}, {"file": "libs/shared/src/ui/form-date-picker/form-date-picker.component.spec.ts", "hash": "12794659494538924692", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/ui/form-date-picker/form-date-picker.component.ts", "hash": "14373498734637861694", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@ionic/angular"]}, {"file": "libs/shared/src/ui/header/header.component.html", "hash": "10446092447428862144"}, {"file": "libs/shared/src/ui/header/header.component.scss", "hash": "16535992000152775908"}, {"file": "libs/shared/src/ui/header/header.component.spec.ts", "hash": "4499384199601543057", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/ui/header/header.component.ts", "hash": "5009446633607208187", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons", "npm:@angular/router"]}, {"file": "libs/shared/src/ui/measurement-slider/measurement-slider.component.html", "hash": "1967418758104838105"}, {"file": "libs/shared/src/ui/measurement-slider/measurement-slider.component.scss", "hash": "16339593707398619767"}, {"file": "libs/shared/src/ui/measurement-slider/measurement-slider.component.ts", "hash": "14443231792646945394", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:@ionic/angular"]}, {"file": "libs/shared/src/ui/multi-thumb-circ-slider/multi-thumb-circ-slider.component.html", "hash": "90983767666276795"}, {"file": "libs/shared/src/ui/multi-thumb-circ-slider/multi-thumb-circ-slider.component.scss", "hash": "15044721303210077016"}, {"file": "libs/shared/src/ui/multi-thumb-circ-slider/multi-thumb-circ-slider.component.spec.ts", "hash": "5874646738943660846", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/ui/multi-thumb-circ-slider/multi-thumb-circ-slider.component.ts", "hash": "14822456602140449247", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms"]}, {"file": "libs/shared/src/ui/radial-progress/radial-progress.component.html", "hash": "10508502904800684804"}, {"file": "libs/shared/src/ui/radial-progress/radial-progress.component.scss", "hash": "1985814668691491674"}, {"file": "libs/shared/src/ui/radial-progress/radial-progress.component.ts", "hash": "9260208285471290787", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/ui/veggies-fruits-radial-slider/greens-radial-slider.component.html", "hash": "10919311977279177162"}, {"file": "libs/shared/src/ui/veggies-fruits-radial-slider/greens-radial-slider.component.scss", "hash": "381268776152645136"}, {"file": "libs/shared/src/ui/veggies-fruits-radial-slider/greens-radial-slider.component.ts", "hash": "3617008941745544831", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@ionic/angular"]}, {"file": "libs/shared/src/ui/water-radial-slider/water-radial-slider.component.html", "hash": "16747688113998276040"}, {"file": "libs/shared/src/ui/water-radial-slider/water-radial-slider.component.scss", "hash": "16523931639935912580"}, {"file": "libs/shared/src/ui/water-radial-slider/water-radial-slider.component.spec.ts", "hash": "6000208595640540936", "deps": ["npm:@angular/core"]}, {"file": "libs/shared/src/ui/water-radial-slider/water-radial-slider.component.ts", "hash": "14544710420958811553", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@ionic/angular"]}, {"file": "libs/shared/tsconfig.json", "hash": "1083278824072282325"}, {"file": "libs/shared/tsconfig.lib.json", "hash": "5796668385415907511"}, {"file": "libs/shared/tsconfig.spec.json", "hash": "2927375835638640771"}], "commons": [{"file": "libs/commons/.eslintrc.json", "hash": "9628345067573567197"}, {"file": "libs/commons/README.md", "hash": "8728086842938397078"}, {"file": "libs/commons/jest.config.ts", "hash": "11713564271122624776"}, {"file": "libs/commons/project.json", "hash": "14040784239488978113"}, {"file": "libs/commons/src/index.ts", "hash": "13676450305127230860"}, {"file": "libs/commons/src/lib/commons/commons.component.html", "hash": "12810695401027375565"}, {"file": "libs/commons/src/lib/commons/commons.component.scss", "hash": "3244421341483603138"}, {"file": "libs/commons/src/lib/commons/commons.component.spec.ts", "hash": "6912953284507458938", "deps": ["npm:@angular/core"]}, {"file": "libs/commons/src/lib/commons/commons.component.ts", "hash": "5291261177390352504", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/commons/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/commons/tsconfig.json", "hash": "1083278824072282325"}, {"file": "libs/commons/tsconfig.lib.json", "hash": "5796668385415907511"}, {"file": "libs/commons/tsconfig.spec.json", "hash": "2927375835638640771"}], "user-profile": [{"file": "libs/user-profile/.eslintrc.json", "hash": "9628345067573567197"}, {"file": "libs/user-profile/README.md", "hash": "9879264375206631268"}, {"file": "libs/user-profile/jest.config.ts", "hash": "437864017620747914"}, {"file": "libs/user-profile/project.json", "hash": "8323358937934478979"}, {"file": "libs/user-profile/src/index.ts", "hash": "17220877980855015471"}, {"file": "libs/user-profile/src/lib/commons/user-diet-form/user-diet-form.component.html", "hash": "16952777962743649838"}, {"file": "libs/user-profile/src/lib/commons/user-diet-form/user-diet-form.component.scss", "hash": "1322938854137371092"}, {"file": "libs/user-profile/src/lib/commons/user-diet-form/user-diet-form.component.spec.ts", "hash": "3196529534368954644", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/commons/user-diet-form/user-diet-form.component.ts", "hash": "10577899052104055962", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/router", "npm:ionicons", "npm:@angular/forms"]}, {"file": "libs/user-profile/src/lib/commons/user-personal-data-form/user-personal-data-form.component.html", "hash": "14231890004625499462"}, {"file": "libs/user-profile/src/lib/commons/user-personal-data-form/user-personal-data-form.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/commons/user-personal-data-form/user-personal-data-form.component.spec.ts", "hash": "17547739216684129222", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/commons/user-personal-data-form/user-personal-data-form.component.ts", "hash": "16132235282362580113", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@ionic/angular", "shared"]}, {"file": "libs/user-profile/src/lib/guard/first-login-guard.guard.ts", "hash": "8249177952671570859", "deps": ["npm:@angular/router", "npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/model/user-profile.model.ts", "hash": "4111652179083807084"}, {"file": "libs/user-profile/src/lib/service/user-details.service.ts", "hash": "3203316018403599146", "deps": ["npm:@angular/core", "shared", "npm:rxjs", "auth"]}, {"file": "libs/user-profile/src/lib/state/user-account-settings.store.ts", "hash": "16172547669345930164", "deps": ["npm:@ngrx/signals"]}, {"file": "libs/user-profile/src/lib/state/user-account.store.ts", "hash": "13950647266502502639", "deps": ["npm:@ngrx/signals"]}, {"file": "libs/user-profile/src/lib/state/user-details.store.ts", "hash": "2968137605579289171", "deps": ["npm:@ngrx/signals", "npm:@angular/core", "npm:rxjs", "npm:@ngrx/operators", "shared"]}, {"file": "libs/user-profile/src/lib/state/user-medical-data.store.ts", "hash": "6516561674749767767", "deps": ["npm:@ngrx/signals"]}, {"file": "libs/user-profile/src/lib/state/user-preferences.store.ts", "hash": "12676513160599153603", "deps": ["npm:@ngrx/signals"]}, {"file": "libs/user-profile/src/lib/state/user-profile-data.store.ts", "hash": "12580834028066318630", "deps": ["npm:@angular/core", "npm:@ngrx/signals"]}, {"file": "libs/user-profile/src/lib/state/user-profile.store.ts", "hash": "6752224302294746260", "deps": ["npm:@ngrx/signals"]}, {"file": "libs/user-profile/src/lib/user-profile.routes.ts", "hash": "11214181858571213911", "deps": ["npm:@angular/router"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-home.component.html", "hash": "7633119593239966019"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-home.component.scss", "hash": "6858099190309411319"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-home.component.spec.ts", "hash": "5278747827013181793", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-home.component.ts", "hash": "17287239421915019028", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/carbohydrates/carbohydrates.component.html", "hash": "14244987115630475411"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/carbohydrates/carbohydrates.component.scss", "hash": "13252415094717498453"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/carbohydrates/carbohydrates.component.ts", "hash": "6554646384816897929", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms", "shared"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/fats-fermented/fats-fermented.component.html", "hash": "1090939677145952678"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/fats-fermented/fats-fermented.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/fats-fermented/fats-fermented.component.ts", "hash": "1959958076549799466", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms", "shared"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/hydration-alcohol/hydration-alcohol.component.html", "hash": "15893716322969955978"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/hydration-alcohol/hydration-alcohol.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/hydration-alcohol/hydration-alcohol.component.ts", "hash": "10340808003639669903", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "shared"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/mental-activity/mental-activity.component.html", "hash": "14888748240314087927"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/mental-activity/mental-activity.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/mental-activity/mental-activity.component.ts", "hash": "14489288242750956746", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "shared"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/physical-activity/physical-activity.component.html", "hash": "2805689588384356565"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/physical-activity/physical-activity.component.scss", "hash": "9302686878683092400"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/physical-activity/physical-activity.component.ts", "hash": "5925269165777971385", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "shared"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/proteins/proteins.component.html", "hash": "2240094970705511887"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/proteins/proteins.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/proteins/proteins.component.ts", "hash": "13184409180889594144", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms", "shared"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/sugar-salt-ultraprocessed/sugar-salt-ultraprocessed.component.html", "hash": "1390600223839371684"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/sugar-salt-ultraprocessed/sugar-salt-ultraprocessed.component.scss", "hash": "7091222570744885685"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/sugar-salt-ultraprocessed/sugar-salt-ultraprocessed.component.ts", "hash": "3597806664084597750", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "shared"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/user-profile-lifestyle.component.html", "hash": "9525950197362920051"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/user-profile-lifestyle.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/user-profile-lifestyle.component.spec.ts", "hash": "14270802260374615658", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/user-profile-lifestyle.component.ts", "hash": "6586617364800030352", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/vitamins-minerals/vitamins-minerals.component.html", "hash": "15144314514592090720"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/vitamins-minerals/vitamins-minerals.component.scss", "hash": "17781929431569975654"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-lifestyle/vitamins-minerals/vitamins-minerals.component.ts", "hash": "17718485872031240864", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "shared"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-medical-data/user-medical-data.component.html", "hash": "3344966539216358932"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-medical-data/user-medical-data.component.scss", "hash": "14650873974507973542"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-medical-data/user-medical-data.component.spec.ts", "hash": "13874265005049939262", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-medical-data/user-medical-data.component.ts", "hash": "2547720033337941874", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms", "npm:ionicons"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-objectives/custom-svg-icon/custom-svg-icon.component.ts", "hash": "15855770504157712303", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/platform-browser"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-objectives/user-profile-macros-objectives/user-profile-macros-objectives.component.html", "hash": "5543314095511055197"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-objectives/user-profile-macros-objectives/user-profile-macros-objectives.component.scss", "hash": "16321319009988244300"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-objectives/user-profile-macros-objectives/user-profile-macros-objectives.component.spec.ts", "hash": "9806599607541512271", "deps": ["npm:@angular/core", "npm:@ionic/angular", "npm:@angular/platform-browser"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-objectives/user-profile-macros-objectives/user-profile-macros-objectives.component.ts", "hash": "10465169719680949135", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms", "shared"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-objectives/user-profile-objectives.component.html", "hash": "1409928734329767402"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-objectives/user-profile-objectives.component.scss", "hash": "17418297576885569694"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-objectives/user-profile-objectives.component.spec.ts", "hash": "10299606785547888337", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-objectives/user-profile-objectives.component.ts", "hash": "13525582678148480642", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons", "npm:@angular/forms"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/bmi-indicator/bmi-indicator.component.html", "hash": "17859302996620845103"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/bmi-indicator/bmi-indicator.component.scss", "hash": "9333268207502838473"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/bmi-indicator/bmi-indicator.component.spec.ts", "hash": "9583450479894506177", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/bmi-indicator/bmi-indicator.component.ts", "hash": "12577914357035019803", "deps": ["npm:@angular/core", "npm:@ionic/angular", "npm:@angular/common"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement-female/user-profile-measurement-female.component.html", "hash": "10821661591112551105"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement-female/user-profile-measurement-female.component.scss", "hash": "16394655969249112474"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement-female/user-profile-measurement-female.component.spec.ts", "hash": "14491281201934625483", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement-female/user-profile-measurement-female.component.ts", "hash": "16604671091836001695", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:d3", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement-male/user-profile-measurement-male.component.html", "hash": "8068661705460382164"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement-male/user-profile-measurement-male.component.scss", "hash": "16408239787127067178"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement-male/user-profile-measurement-male.component.spec.ts", "hash": "6516363981644993349", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement-male/user-profile-measurement-male.component.ts", "hash": "4447742414649614821", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:d3", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement.component.html", "hash": "15326341158100718670"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement.component.spec.ts", "hash": "16308196080548058366", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-measurement/user-profile-measurement.component.ts", "hash": "8594115612972733948", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-personal-data.component.html", "hash": "14220059342698795893"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-personal-data.component.scss", "hash": "10573112636715951542"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-personal-data.component.spec.ts", "hash": "13254513421834797813", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-personal-data/user-profile-personal-data.component.ts", "hash": "4044217439352959991", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms", "shared", "npm:ionicons"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-complexity/nutrition-needs-complexity.component.html", "hash": "2596185198141920406"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-complexity/nutrition-needs-complexity.component.scss", "hash": "5374565266286372565"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-complexity/nutrition-needs-complexity.component.spec.ts", "hash": "1320539648082307302", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-complexity/nutrition-needs-complexity.component.ts", "hash": "6689710393199903613", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms", "npm:@angular/platform-browser"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-diet-type/nutrition-needs-diet-type.component.html", "hash": "13113165558737816774"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-diet-type/nutrition-needs-diet-type.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-diet-type/nutrition-needs-diet-type.component.spec.ts", "hash": "10993742086141652601", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-diet-type/nutrition-needs-diet-type.component.ts", "hash": "4930384213150358949", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-ingredient-costs/nutrition-needs-ingredient-costs.component.html", "hash": "17039768368469065777"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-ingredient-costs/nutrition-needs-ingredient-costs.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-ingredient-costs/nutrition-needs-ingredient-costs.component.spec.ts", "hash": "9323524433111543031", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-ingredient-costs/nutrition-needs-ingredient-costs.component.ts", "hash": "9693301616397593279", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-preparation-time/nutrition-needs-preparation-time.component.html", "hash": "931297646617828439"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-preparation-time/nutrition-needs-preparation-time.component.scss", "hash": "13157426345687728884"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-preparation-time/nutrition-needs-preparation-time.component.spec.ts", "hash": "16371639920332646102", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-preparation-time/nutrition-needs-preparation-time.component.ts", "hash": "4545359336612825662", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms", "npm:@angular/platform-browser"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-region/nutrition-needs-region.component.html", "hash": "13607168478910520951"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-region/nutrition-needs-region.component.scss", "hash": "6038026452812870791"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-region/nutrition-needs-region.component.spec.ts", "hash": "16301285243324703107", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-region/nutrition-needs-region.component.ts", "hash": "8878341838190093031", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-sources/nutrition-needs-sources.component.html", "hash": "1858669126916430818"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-sources/nutrition-needs-sources.component.scss", "hash": "16410939810381584268"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-sources/nutrition-needs-sources.component.spec.ts", "hash": "1142427160763381464", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-sources/nutrition-needs-sources.component.ts", "hash": "4375121557903187162", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:ionicons", "npm:@ionic/angular"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-taste/nutrition-needs-taste.component.html", "hash": "5101826249460885565"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-taste/nutrition-needs-taste.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-taste/nutrition-needs-taste.component.spec.ts", "hash": "923713247304690618", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-taste/nutrition-needs-taste.component.ts", "hash": "6577029571954086320", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-tools/nutrition-needs-tools.component.html", "hash": "13190033259660423022"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-tools/nutrition-needs-tools.component.scss", "hash": "13750733367856796818"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-tools/nutrition-needs-tools.component.spec.ts", "hash": "3560777290358487993", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/nutrition-needs-tools/nutrition-needs-tools.component.ts", "hash": "9404234082766739539", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/user-profile-preferencess-needs-nutrition.component.html", "hash": "4017562833152219003"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/user-profile-preferencess-needs-nutrition.component.scss", "hash": "11298937040583269602"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-nutrition/user-profile-preferencess-needs-nutrition.component.ts", "hash": "17853408213530173936", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@ionic/angular", "npm:ionicons", "npm:@angular/platform-browser"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-physical-activity/user-profile-preferencess-needs-physical-activity.component.html", "hash": "15488378701814935147"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-physical-activity/user-profile-preferencess-needs-physical-activity.component.scss", "hash": "10963549027012124046"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs-physical-activity/user-profile-preferencess-needs-physical-activity.component.ts", "hash": "1951461761682342393", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs.component.html", "hash": "10120679987472546891"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs.component.spec.ts", "hash": "5948173846382181989", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-preferencess-needs/user-profile-preferencess-needs.component.ts", "hash": "3027342674490296045", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-wearables/user-wearables.component.html", "hash": "5431022200428013139"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-wearables/user-wearables.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-wearables/user-wearables.component.spec.ts", "hash": "8804455544812217240", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-home/user-profile-wearables/user-wearables.component.ts", "hash": "1685069139993547824", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-profile-settings.component.html", "hash": "4713846781319242275"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-profile-settings.component.scss", "hash": "329536145098332849"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-profile-settings.component.spec.ts", "hash": "14833757508036803314", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-profile-settings.component.ts", "hash": "1550417187612720370", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router", "npm:@ionic/angular", "npm:@angular/forms"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-customization/user-settings-customization.component.html", "hash": "8903777729733172448"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-customization/user-settings-customization.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-customization/user-settings-customization.component.spec.ts", "hash": "16616363565818652090", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-customization/user-settings-customization.component.ts", "hash": "8647568134877108149", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-notifications/user-settings-notifications.component.html", "hash": "17415609308608539364"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-notifications/user-settings-notifications.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-notifications/user-settings-notifications.component.spec.ts", "hash": "17318404241412523952", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-notifications/user-settings-notifications.component.ts", "hash": "8703193633069370452", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-reset-password/user-settings-reset-password.component.html", "hash": "11291872247657499228"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-reset-password/user-settings-reset-password.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-reset-password/user-settings-reset-password.component.spec.ts", "hash": "6911103148551667096", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-reset-password/user-settings-reset-password.component.ts", "hash": "15440204545365059421", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-subscription/user-settings-subscription.component.html", "hash": "5543999841381471686"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-subscription/user-settings-subscription.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-subscription/user-settings-subscription.component.spec.ts", "hash": "6986248634319760540", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-settings/user-settings-subscription/user-settings-subscription.component.ts", "hash": "1059474906661736090", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-wrapper/user-profile-wrapper.component.html", "hash": "849583666502130815"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-wrapper/user-profile-wrapper.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-wrapper/user-profile-wrapper.component.spec.ts", "hash": "16616818320877176081", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-profile/user-profile-wrapper/user-profile-wrapper.component.ts", "hash": "563352636326200523", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@ionic/angular"]}, {"file": "libs/user-profile/src/lib/user-start/user-profile-personal-details/user-profile-personal-details.component.html", "hash": "16071852535626733736"}, {"file": "libs/user-profile/src/lib/user-start/user-profile-personal-details/user-profile-personal-details.component.scss", "hash": "15243640224116066541"}, {"file": "libs/user-profile/src/lib/user-start/user-profile-personal-details/user-profile-personal-details.component.spec.ts", "hash": "5283685420640615297", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-start/user-profile-personal-details/user-profile-personal-details.component.ts", "hash": "16641625833912198702", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/router", "npm:@ionic/angular", "shared"]}, {"file": "libs/user-profile/src/lib/user-start/user-profile-questionnaire/user-profile-questionnaire.component.html", "hash": "17113026112083233377"}, {"file": "libs/user-profile/src/lib/user-start/user-profile-questionnaire/user-profile-questionnaire.component.scss", "hash": "12716779240891369802"}, {"file": "libs/user-profile/src/lib/user-start/user-profile-questionnaire/user-profile-questionnaire.component.spec.ts", "hash": "2810393506085156855", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-start/user-profile-questionnaire/user-profile-questionnaire.component.ts", "hash": "13270914919642599075", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/user-profile/src/lib/user-start/user-start-activity/user-start-activity.component.html", "hash": "16393879243204099650"}, {"file": "libs/user-profile/src/lib/user-start/user-start-activity/user-start-activity.component.scss", "hash": "14954899914939821378"}, {"file": "libs/user-profile/src/lib/user-start/user-start-activity/user-start-activity.component.ts", "hash": "4541800823049197175", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:ionicons", "npm:@angular/router"]}, {"file": "libs/user-profile/src/lib/user-start/user-start-alergies/user-start-alergies.component.html", "hash": "13816398046097739520"}, {"file": "libs/user-profile/src/lib/user-start/user-start-alergies/user-start-alergies.component.scss", "hash": "13555453071214314963"}, {"file": "libs/user-profile/src/lib/user-start/user-start-alergies/user-start-alergies.component.spec.ts", "hash": "10789478307473394266", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-start/user-start-alergies/user-start-alergies.component.ts", "hash": "16754192063885556543", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/router", "npm:@ionic/angular"]}, {"file": "libs/user-profile/src/lib/user-start/user-start-diet/user-start-diet.component.html", "hash": "12726618951148293726"}, {"file": "libs/user-profile/src/lib/user-start/user-start-diet/user-start-diet.component.scss", "hash": "3244421341483603138"}, {"file": "libs/user-profile/src/lib/user-start/user-start-diet/user-start-diet.component.spec.ts", "hash": "13165803020926275457", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-start/user-start-diet/user-start-diet.component.ts", "hash": "17239343001503065916", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/forms", "npm:ionicons", "npm:@angular/router"]}, {"file": "libs/user-profile/src/lib/user-start/user-start-name/user-start-name.component.html", "hash": "1747596973417863342"}, {"file": "libs/user-profile/src/lib/user-start/user-start-name/user-start-name.component.scss", "hash": "10191584098251930137"}, {"file": "libs/user-profile/src/lib/user-start/user-start-name/user-start-name.component.spec.ts", "hash": "14886829890190447835", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-start/user-start-name/user-start-name.component.ts", "hash": "949971701296006314", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:@ionic/angular", "npm:@angular/router", "npm:ionicons"]}, {"file": "libs/user-profile/src/lib/user-start/user-start-objective/user-start-objective.component.html", "hash": "5402558504193922326"}, {"file": "libs/user-profile/src/lib/user-start/user-start-objective/user-start-objective.component.scss", "hash": "7301891759909169635"}, {"file": "libs/user-profile/src/lib/user-start/user-start-objective/user-start-objective.component.spec.ts", "hash": "10318462760203515472", "deps": ["npm:@angular/core"]}, {"file": "libs/user-profile/src/lib/user-start/user-start-objective/user-start-objective.component.ts", "hash": "14692235433630787117", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/router", "npm:ionicons"]}, {"file": "libs/user-profile/src/lib/user-start/user-start.component.html", "hash": "15716709926094927428"}, {"file": "libs/user-profile/src/lib/user-start/user-start.component.scss", "hash": "16384236252758644808"}, {"file": "libs/user-profile/src/lib/user-start/user-start.component.ts", "hash": "13817858904409934123", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "npm:@angular/router", "npm:ionicons"]}, {"file": "libs/user-profile/src/test-setup.ts", "hash": "12935735732682149507", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/user-profile/tsconfig.json", "hash": "1083278824072282325"}, {"file": "libs/user-profile/tsconfig.lib.json", "hash": "10909350098281080012"}, {"file": "libs/user-profile/tsconfig.spec.json", "hash": "2927375835638640771"}], "explorer": [{"file": "libs/explorer/.eslintrc.json", "hash": "9628345067573567197"}, {"file": "libs/explorer/README.md", "hash": "3510210275088638626"}, {"file": "libs/explorer/jest.config.ts", "hash": "15645507647502106863"}, {"file": "libs/explorer/project.json", "hash": "17129504418731528858"}, {"file": "libs/explorer/src/index.ts", "hash": "10995583070091277361"}, {"file": "libs/explorer/src/lib/explorer.routes.ts", "hash": "1156235639826823779", "deps": ["npm:@angular/router"]}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-mental/breathing-exercise/breathing-exercise.component.html", "hash": "15753061915348621310"}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-mental/breathing-exercise/breathing-exercise.component.scss", "hash": "13687766265381168461"}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-mental/breathing-exercise/breathing-exercise.component.spec.ts", "hash": "11432189173856509520", "deps": ["npm:@angular/core"]}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-mental/breathing-exercise/breathing-exercise.component.ts", "hash": "6250175421142410760", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@ionic/angular", "npm:ionicons"]}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-mental/explore-dashboard-mental.component.html", "hash": "2208377323709545191"}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-mental/explore-dashboard-mental.component.scss", "hash": "3244421341483603138"}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-mental/explore-dashboard-mental.component.spec.ts", "hash": "11035389092348304415", "deps": ["npm:@angular/core"]}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-mental/explore-dashboard-mental.component.ts", "hash": "16264005892828884395", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-nutrition/explore-dashboard-nutrition.component.html", "hash": "11219859778917932130"}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-nutrition/explore-dashboard-nutrition.component.scss", "hash": "3244421341483603138"}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-nutrition/explore-dashboard-nutrition.component.spec.ts", "hash": "14555461872536848255", "deps": ["npm:@angular/core"]}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-nutrition/explore-dashboard-nutrition.component.ts", "hash": "243117269319157132", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular"]}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-workout/explore-dashboard-workout.component.html", "hash": "18254601779027738652"}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-workout/explore-dashboard-workout.component.scss", "hash": "5932956805563321666"}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-workout/explore-dashboard-workout.component.spec.ts", "hash": "16470605677145919566", "deps": ["npm:@angular/core"]}, {"file": "libs/explorer/src/lib/explorer/explore-dashboard-workout/explore-dashboard-workout.component.ts", "hash": "13392955630581499618", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@ionic/angular", "shared"]}, {"file": "libs/explorer/src/lib/explorer/explorer.component.css", "hash": "18201652648715918506"}, {"file": "libs/explorer/src/lib/explorer/explorer.component.html", "hash": "12500091195051683163"}, {"file": "libs/explorer/src/lib/explorer/explorer.component.spec.ts", "hash": "2999922046508728088", "deps": ["npm:@angular/core"]}, {"file": "libs/explorer/src/lib/explorer/explorer.component.ts", "hash": "16009277643237298862", "deps": ["npm:@angular/core", "npm:@angular/common", "shared", "npm:@ionic/angular", "user-profile", "npm:ionicons"]}, {"file": "libs/explorer/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/explorer/tsconfig.json", "hash": "1083278824072282325"}, {"file": "libs/explorer/tsconfig.lib.json", "hash": "5796668385415907511"}, {"file": "libs/explorer/tsconfig.spec.json", "hash": "2927375835638640771"}], "playground": [{"file": "libs/playground/.eslintrc.json", "hash": "9628345067573567197"}, {"file": "libs/playground/README.md", "hash": "12076422824566450055"}, {"file": "libs/playground/jest.config.ts", "hash": "10045781298510534775"}, {"file": "libs/playground/project.json", "hash": "11572602096039115546"}, {"file": "libs/playground/src/index.ts", "hash": "2209058926120016850"}, {"file": "libs/playground/src/lib/playground/playground.component.css", "hash": "3244421341483603138"}, {"file": "libs/playground/src/lib/playground/playground.component.html", "hash": "12030399462359451770"}, {"file": "libs/playground/src/lib/playground/playground.component.spec.ts", "hash": "327543095110681403", "deps": ["npm:@angular/core"]}, {"file": "libs/playground/src/lib/playground/playground.component.ts", "hash": "9417355060709110842", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/playground/src/test-setup.ts", "hash": "12935735732682149507", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/playground/tsconfig.json", "hash": "1083278824072282325"}, {"file": "libs/playground/tsconfig.lib.json", "hash": "5796668385415907511"}, {"file": "libs/playground/tsconfig.spec.json", "hash": "2927375835638640771"}], "shop": [{"file": "libs/shop/.eslintrc.json", "hash": "9628345067573567197"}, {"file": "libs/shop/README.md", "hash": "10076686953668138921"}, {"file": "libs/shop/jest.config.ts", "hash": "11002190444682439783"}, {"file": "libs/shop/project.json", "hash": "15095428948340670316"}, {"file": "libs/shop/src/index.ts", "hash": "4517034450943514890"}, {"file": "libs/shop/src/lib/shop/shop.component.css", "hash": "3244421341483603138"}, {"file": "libs/shop/src/lib/shop/shop.component.html", "hash": "11346905874749916738"}, {"file": "libs/shop/src/lib/shop/shop.component.spec.ts", "hash": "4165935920505039708", "deps": ["npm:@angular/core"]}, {"file": "libs/shop/src/lib/shop/shop.component.ts", "hash": "12596756453070230491", "deps": ["npm:@angular/core", "npm:@angular/common", "shared"]}, {"file": "libs/shop/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "libs/shop/tsconfig.json", "hash": "1083278824072282325"}, {"file": "libs/shop/tsconfig.lib.json", "hash": "5796668385415907511"}, {"file": "libs/shop/tsconfig.spec.json", "hash": "2927375835638640771"}]}, "nonProjectFiles": [{"file": ".cursor/mcp.json", "hash": "13705131632729848352"}, {"file": ".cursor/rules/angular-components-rule.mdc", "hash": "13729929005457495834"}, {"file": ".cursor/rules/angular-signals-store.mdc", "hash": "1445505648567647107"}, {"file": ".cursor/rules/cursor_rules.mdc", "hash": "13635137457673500577"}, {"file": ".cursor/rules/dev_workflow.mdc", "hash": "15891570825346697845"}, {"file": ".cursor/rules/ionic-rules.mdc", "hash": "10840491452355685284"}, {"file": ".cursor/rules/nx-commands.mdc", "hash": "8604151345180510075"}, {"file": ".cursor/rules/nx-rules.mdc", "hash": "8975215422716898164"}, {"file": ".cursor/rules/self_improve.mdc", "hash": "930226137200821915"}, {"file": ".cursor/rules/taskmaster.mdc", "hash": "8962244929938605615"}, {"file": ".cursor/todo.md", "hash": "15317045617295399980"}, {"file": ".editorconfig", "hash": "11532813898455409296"}, {"file": ".env.example", "hash": "13510839508906918578"}, {"file": ".es<PERSON><PERSON><PERSON>", "hash": "13404745306227114823"}, {"file": ".eslintrc.json", "hash": "4566917484430375526"}, {"file": ".github/instructions/nx.instructions.md", "hash": "15721388765970466335"}, {"file": ".giti<PERSON>re", "hash": "2244886871377993754"}, {"file": ".prettieri<PERSON>re", "hash": "2824663867028258575"}, {"file": ".prettier<PERSON>", "hash": "6820402234827505055"}, {"file": ".taskmaster/config.json", "hash": "16206679580333672394"}, {"file": ".taskmaster/templates/example_prd.txt", "hash": "7466167600502466891"}, {"file": "README.md", "hash": "13216134041339922092"}, {"file": "apps/test-2/.browserslistrc", "hash": "8928530114349553238"}, {"file": "apps/test-2/.editorconfig", "hash": "17241496168097493888"}, {"file": "apps/test-2/.eslintrc.json", "hash": "3555539555184033037"}, {"file": "apps/test-2/.gitignore", "hash": "2037388041983497831"}, {"file": "apps/test-2/angular.json", "hash": "1647443398440780901"}, {"file": "apps/test-2/capacitor.config.ts", "hash": "10411749053381203046"}, {"file": "apps/test-2/ionic.config.json", "hash": "14439038619542199589"}, {"file": "apps/test-2/karma.conf.js", "hash": "15586218685649057480"}, {"file": "apps/test-2/package-lock.json", "hash": "7794381104448101048"}, {"file": "apps/test-2/package.json", "hash": "6307562197581711317"}, {"file": "apps/test-2/resources/icon.png", "hash": "1337829275635163250"}, {"file": "apps/test-2/resources/splash.png", "hash": "7394538660580956099"}, {"file": "apps/test-2/src/app/app-routing.module.ts", "hash": "6864750154150207358"}, {"file": "apps/test-2/src/app/app.component.html", "hash": "1863388359251399181"}, {"file": "apps/test-2/src/app/app.component.scss", "hash": "3244421341483603138"}, {"file": "apps/test-2/src/app/app.component.spec.ts", "hash": "8509923302263693287"}, {"file": "apps/test-2/src/app/app.component.ts", "hash": "3442851017626927042"}, {"file": "apps/test-2/src/app/app.module.ts", "hash": "9314004463298906653"}, {"file": "apps/test-2/src/app/home/<USER>", "hash": "13786966097019557101"}, {"file": "apps/test-2/src/app/home/<USER>", "hash": "7903437783807577612"}, {"file": "apps/test-2/src/app/home/<USER>", "hash": "5398836310047677551"}, {"file": "apps/test-2/src/app/home/<USER>", "hash": "3244421341483603138"}, {"file": "apps/test-2/src/app/home/<USER>", "hash": "1187091672186836020"}, {"file": "apps/test-2/src/app/home/<USER>", "hash": "10927110708067941400"}, {"file": "apps/test-2/src/app/message/message.component.html", "hash": "1161158437708843872"}, {"file": "apps/test-2/src/app/message/message.component.scss", "hash": "3265080832061332093"}, {"file": "apps/test-2/src/app/message/message.component.spec.ts", "hash": "1573880399056332203"}, {"file": "apps/test-2/src/app/message/message.component.ts", "hash": "183175221570648110"}, {"file": "apps/test-2/src/app/message/message.module.ts", "hash": "15505213497522889097"}, {"file": "apps/test-2/src/app/services/data.service.spec.ts", "hash": "164998096750463509"}, {"file": "apps/test-2/src/app/services/data.service.ts", "hash": "9965942808194437535"}, {"file": "apps/test-2/src/app/view-message/view-message-routing.module.ts", "hash": "1142906107773545256"}, {"file": "apps/test-2/src/app/view-message/view-message.module.ts", "hash": "5904110429414096358"}, {"file": "apps/test-2/src/app/view-message/view-message.page.html", "hash": "11713741798510220927"}, {"file": "apps/test-2/src/app/view-message/view-message.page.scss", "hash": "15091047814770514042"}, {"file": "apps/test-2/src/app/view-message/view-message.page.spec.ts", "hash": "6948245613066368162"}, {"file": "apps/test-2/src/app/view-message/view-message.page.ts", "hash": "12315548084399335384"}, {"file": "apps/test-2/src/assets/icon/favicon.png", "hash": "17482944836898362639"}, {"file": "apps/test-2/src/assets/shapes.svg", "hash": "6713572288142939778"}, {"file": "apps/test-2/src/environments/environment.prod.ts", "hash": "11847377846594200203"}, {"file": "apps/test-2/src/environments/environment.ts", "hash": "14410313991637913761"}, {"file": "apps/test-2/src/global.scss", "hash": "12223180827798948429"}, {"file": "apps/test-2/src/index.html", "hash": "12859993114988254134"}, {"file": "apps/test-2/src/main.ts", "hash": "12459757308362336283"}, {"file": "apps/test-2/src/polyfills.ts", "hash": "6334563476032399242"}, {"file": "apps/test-2/src/test.ts", "hash": "7876741224155508608"}, {"file": "apps/test-2/src/theme/variables.scss", "hash": "3760878989438468911"}, {"file": "apps/test-2/src/zone-flags.ts", "hash": "6117193595888083885"}, {"file": "apps/test-2/tsconfig.app.json", "hash": "7685451593566938896"}, {"file": "apps/test-2/tsconfig.json", "hash": "14025965691221551535"}, {"file": "apps/test-2/tsconfig.spec.json", "hash": "14277469517724802009"}, {"file": "apps/test-3/.browserslistrc", "hash": "8928530114349553238"}, {"file": "apps/test-3/.editorconfig", "hash": "17241496168097493888"}, {"file": "apps/test-3/.eslintrc.json", "hash": "3555539555184033037"}, {"file": "apps/test-3/.gitignore", "hash": "2037388041983497831"}, {"file": "apps/test-3/angular.json", "hash": "1647443398440780901"}, {"file": "apps/test-3/capacitor.config.ts", "hash": "10338518576892408611"}, {"file": "apps/test-3/ionic.config.json", "hash": "10420398669900553102"}, {"file": "apps/test-3/karma.conf.js", "hash": "15586218685649057480"}, {"file": "apps/test-3/package-lock.json", "hash": "*****************"}, {"file": "apps/test-3/package.json", "hash": "7561006290874073494"}, {"file": "apps/test-3/resources/icon.png", "hash": "1057372575889840117"}, {"file": "apps/test-3/resources/splash.png", "hash": "2682536358452365922"}, {"file": "apps/test-3/src/app/app-routing.module.ts", "hash": "18406785908517499937"}, {"file": "apps/test-3/src/app/app.component.html", "hash": "5761998008662771754"}, {"file": "apps/test-3/src/app/app.component.scss", "hash": "725504766403951860"}, {"file": "apps/test-3/src/app/app.component.spec.ts", "hash": "8995189379509824605"}, {"file": "apps/test-3/src/app/app.component.ts", "hash": "15037303187015902985"}, {"file": "apps/test-3/src/app/app.module.ts", "hash": "12114793843511660159"}, {"file": "apps/test-3/src/app/folder/folder-routing.module.ts", "hash": "11221433021134652442"}, {"file": "apps/test-3/src/app/folder/folder.module.ts", "hash": "16698637248064392709"}, {"file": "apps/test-3/src/app/folder/folder.page.html", "hash": "8957061632153817772"}, {"file": "apps/test-3/src/app/folder/folder.page.scss", "hash": "15290328609562197644"}, {"file": "apps/test-3/src/app/folder/folder.page.spec.ts", "hash": "2370043544416927716"}, {"file": "apps/test-3/src/app/folder/folder.page.ts", "hash": "14783797269577101450"}, {"file": "apps/test-3/src/assets/icon/favicon.png", "hash": "17482944836898362639"}, {"file": "apps/test-3/src/assets/shapes.svg", "hash": "6713572288142939778"}, {"file": "apps/test-3/src/environments/environment.prod.ts", "hash": "11847377846594200203"}, {"file": "apps/test-3/src/environments/environment.ts", "hash": "14410313991637913761"}, {"file": "apps/test-3/src/global.scss", "hash": "12223180827798948429"}, {"file": "apps/test-3/src/index.html", "hash": "12859993114988254134"}, {"file": "apps/test-3/src/main.ts", "hash": "12459757308362336283"}, {"file": "apps/test-3/src/polyfills.ts", "hash": "6334563476032399242"}, {"file": "apps/test-3/src/test.ts", "hash": "7876741224155508608"}, {"file": "apps/test-3/src/theme/variables.scss", "hash": "3760878989438468911"}, {"file": "apps/test-3/src/zone-flags.ts", "hash": "6117193595888083885"}, {"file": "apps/test-3/tsconfig.app.json", "hash": "7685451593566938896"}, {"file": "apps/test-3/tsconfig.json", "hash": "14025965691221551535"}, {"file": "apps/test-3/tsconfig.spec.json", "hash": "14277469517724802009"}, {"file": "assets/FreeSample-Vectorizer-io-Untitled.png", "hash": "16688185898767573075"}, {"file": "assets/diet_706195.png", "hash": "10962051168842848362"}, {"file": "assets/icon.png", "hash": "17287908088671890813"}, {"file": "assets/image.png", "hash": "1094189093816137239"}, {"file": "assets/shake-hands.png", "hash": "4206608587151033079"}, {"file": "assets/trainers/andrei.jpg", "hash": "16437096192666741407"}, {"file": "backend/nutrition-master/libs/concepts/pom.xml", "hash": "12174598573098305153"}, {"file": "backend/nutrition-master/libs/concepts/src/main/java/com/nutrition/libs/concepts/config/ConceptsConfig.java", "hash": "3884971512969590290"}, {"file": "backend/nutrition-master/libs/concepts/src/main/java/com/nutrition/libs/concepts/domain/DietType.java", "hash": "2346436299617117342"}, {"file": "backend/nutrition-master/libs/concepts/src/main/java/com/nutrition/libs/concepts/domain/DietTypeEnum.java", "hash": "9922303121163664158"}, {"file": "backend/nutrition-master/libs/eatntrack-crawler/pom.xml", "hash": "7117016408669418351"}, {"file": "backend/nutrition-master/libs/eatntrack-crawler/src/main/java/com/nutrition/libs/eatntrack/crawler/config/EatnTrackCrawlerConfig.java", "hash": "12433334700011717505"}, {"file": "backend/nutrition-master/libs/eatntrack-crawler/src/main/java/com/nutrition/libs/eatntrack/crawler/model/Product.java", "hash": "6546469118884267927"}, {"file": "backend/nutrition-master/libs/eatntrack-crawler/src/main/java/com/nutrition/libs/eatntrack/crawler/processor/EatNTrackCrawlingProcess.java", "hash": "8039135940654114519"}, {"file": "backend/nutrition-master/libs/eatntrack-crawler/src/main/java/com/nutrition/libs/eatntrack/crawler/processor/EatNTrackPageObject.java", "hash": "2260636902096574601"}, {"file": "backend/nutrition-master/libs/jobs/pom.xml", "hash": "17990297446387040631"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/akka/actors/AkkaJobActor.java", "hash": "1360116842688387378"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/akka/actors/AkkaJobManager.java", "hash": "105681046976042942"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/akka/actors/AkkaJobWorker.java", "hash": "3248795220327364379"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/akka/config/AkkaJobConfiguration.java", "hash": "7585486389639848200"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/config/JobsConfiguration.java", "hash": "9966918237228595375"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobDefinition.java", "hash": "17831521338423252080"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobDefinitionRepository.java", "hash": "2755112096453038064"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobDefinitionStatus.java", "hash": "10183952035184193173"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobEntity.java", "hash": "15111684872847860002"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobEntityRepository.java", "hash": "10636978531455572383"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobExecutionLog.java", "hash": "13477360624826129332"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobExecutionLogRepository.java", "hash": "3882170300022828260"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobFieldConstants.java", "hash": "16717151611379344162"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobParameter.java", "hash": "8333274917840321770"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobParameterType.java", "hash": "17915451043868746920"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobStatus.java", "hash": "9621856615212115251"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/JobTransitionException.java", "hash": "3896950939509493939"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/Priority.java", "hash": "12578565803003413520"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/ScheduledItem.java", "hash": "8888621415120447492"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/UUIDGenerator.java", "hash": "8475670298755264776"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/domain/Utilization.java", "hash": "16965688531241715174"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/groovy/GroovyProcessor.java", "hash": "18200233473432636086"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/service/JobPriorityQueue.java", "hash": "8609772216403001784"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/service/JobQueueingService.java", "hash": "9533976909922863680"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/service/JobService.java", "hash": "8724652235333312381"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/service/JobsFacade.java", "hash": "3952154007959521057"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/service/ScheduledItemsService.java", "hash": "12827838828072729615"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/service/Scheduler.java", "hash": "1662384902223379637"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/utils/JsonToJobParameters.java", "hash": "12089746743816953953"}, {"file": "backend/nutrition-master/libs/jobs/src/main/java/com/nutrition/libs/jobs/utils/ParametersResolver.java", "hash": "2282829816216273820"}, {"file": "backend/nutrition-master/libs/jobs/src/test/groovy/com/nutrition/libs/jobs/classes/MyGroovyClass.groovy", "hash": "8202285981646344448"}, {"file": "backend/nutrition-master/libs/jobs/src/test/groovy/com/nutrition/libs/jobs/classes/ParameterScriptGroovy.groovy", "hash": "8085646711286654966"}, {"file": "backend/nutrition-master/libs/jobs/src/test/java/com/nutrition/libs/jobs/groovy/GroovyProcessorTest.java", "hash": "592449914209270427"}, {"file": "backend/nutrition-master/libs/jobs/src/test/java/com/nutrition/libs/jobs/service/BasicTest2.groovy", "hash": "11707399728476633152"}, {"file": "backend/nutrition-master/libs/jobs/src/test/java/com/nutrition/libs/jobs/service/CronExpressionTest.java", "hash": "161553826081530075"}, {"file": "backend/nutrition-master/libs/jobs/src/test/java/com/nutrition/libs/jobs/service/GroovyTest2.groovy", "hash": "12544174665505297935"}, {"file": "backend/nutrition-master/libs/jobs/src/test/java/com/nutrition/libs/jobs/service/JobPriorityQueueTest.java", "hash": "13883027737940837912"}, {"file": "backend/nutrition-master/libs/jobs/src/test/java/com/nutrition/libs/jobs/service/JobQueueingServiceTest.java", "hash": "9068446190668101779"}, {"file": "backend/nutrition-master/libs/jobs/src/test/java/com/nutrition/libs/jobs/service/JobSchedulerTest.java", "hash": "15495606381591611176"}, {"file": "backend/nutrition-master/libs/jobs/src/test/java/com/nutrition/libs/jobs/utils/ParametersResolverTest.java", "hash": "23331656345931797"}, {"file": "backend/nutrition-master/libs/jobs/src/test/resources/scripts/JobThatWaits5Seconds.groovy", "hash": "13541653700126584562"}, {"file": "backend/nutrition-master/libs/jobs/src/test/resources/scripts/customClassesScript.groovy", "hash": "13282480144331341644"}, {"file": "backend/nutrition-master/libs/jobs/src/test/resources/scripts/parametersScript.groovy", "hash": "15722490893303927488"}, {"file": "backend/nutrition-master/libs/localization/pom.xml", "hash": "12586699355817682158"}, {"file": "backend/nutrition-master/libs/localization/src/main/java/com/nutrition/libs/localization/config/LocalizationConfig.java", "hash": "7356618263859715594"}, {"file": "backend/nutrition-master/libs/localization/src/main/java/com/nutrition/libs/localization/domain/Localization.java", "hash": "2308545726531311699"}, {"file": "backend/nutrition-master/libs/localization/src/main/java/com/nutrition/libs/localization/domain/LocalizationEntityType.java", "hash": "16123153541458888378"}, {"file": "backend/nutrition-master/libs/localization/src/main/java/com/nutrition/libs/localization/domain/LocalizationRepository.java", "hash": "4555286644258514575"}, {"file": "backend/nutrition-master/libs/nutrition/pom.xml", "hash": "5657674997853046027"}, {"file": "backend/nutrition-master/libs/nutrition/src/main/java/com/nutrition/libs/nutrition/config/NutritionConfiguration.java", "hash": "6587489032041637607"}, {"file": "backend/nutrition-master/libs/nutrition/src/main/java/com/nutrition/libs/nutrition/domain/NutritionValues.java", "hash": "747073600895642413"}, {"file": "backend/nutrition-master/libs/nutrition/src/main/java/com/nutrition/libs/nutrition/domain/NutritionValuesRepository.java", "hash": "1423305003473776045"}, {"file": "backend/nutrition-master/libs/nutrition/src/main/java/com/nutrition/libs/nutrition/service/NutritionValuesImporter.java", "hash": "1639259627776966474"}, {"file": "backend/nutrition-master/libs/nutrition/src/main/java/com/nutrition/libs/nutrition/service/NutritionValuesService.java", "hash": "9570748383228842530"}, {"file": "backend/nutrition-master/libs/playground/pom.xml", "hash": "6447717055826825534"}, {"file": "backend/nutrition-master/libs/products/pom.xml", "hash": "12023490848388573552"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/config/ProductsConfiguration.java", "hash": "16456216225866887262"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/controller/ProductsController.java", "hash": "6229633059609076534"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/domain/Product.java", "hash": "5969428200308098919"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/domain/ProductDetails.java", "hash": "2108821978384889979"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/domain/ProductRepository.java", "hash": "16132612869564269160"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/domain/ProductRepositoryCustom.java", "hash": "10246859080088754223"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/domain/ProductRepositoryCustomImpl.java", "hash": "7822455089778965748"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/domain/projection/ProductProjection.java", "hash": "6166231289501429144"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/domain/projection/ProductProjectionRepository.java", "hash": "15521496696929823988"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/service/ProductDetailsService.java", "hash": "631433529139929586"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/service/ProductService.java", "hash": "16781451368106833006"}, {"file": "backend/nutrition-master/libs/products/src/main/java/com/nutrition/libs/products/service/dto/ProductNutrition.java", "hash": "17908024310166293286"}, {"file": "backend/nutrition-master/libs/products/src/test/java/com/nutrition/libs/products/ProductsTest.java", "hash": "15875445123631601879"}, {"file": "backend/nutrition-master/libs/questionnaire/pom.xml", "hash": "7182163867980375322"}, {"file": "backend/nutrition-master/libs/questionnaire/questionnaire.iml", "hash": "13165174378417633793"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/config/QuestionnaireConfig.java", "hash": "300218694296214482"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/controller/QuestionnaireController.java", "hash": "11556789589109758635"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/controller/ResponseController.java", "hash": "5759039878388873597"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/domain/Questionnaire.java", "hash": "216206220653238304"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/domain/QuestionnaireAnswer.java", "hash": "4317060944309003396"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/domain/QuestionnaireQuestion.java", "hash": "2474400255814183004"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/domain/QuestionnaireResponse.java", "hash": "9092992944147401920"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/domain/dto/QuestionnaireResponseDTO.java", "hash": "4445708430425260642"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/domain/repository/QuestionnaireAnswerRepository.java", "hash": "9768735614803089539"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/domain/repository/QuestionnaireQuestionRepository.java", "hash": "8168718059443665215"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/domain/repository/QuestionnaireRepository.java", "hash": "4926548773556653521"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/domain/repository/QuestionnaireResponseRepository.java", "hash": "8209254021322439092"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/service/QuestionnaireService.java", "hash": "15268563720939441556"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/service/ResponseService.java", "hash": "9233886906422089142"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/service/dto/AnswerLocalizedDTO.java", "hash": "14004487389867050262"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/service/dto/QuestionLocalizedDTO.java", "hash": "8397667361618419708"}, {"file": "backend/nutrition-master/libs/questionnaire/src/main/java/com/nutrition/libs/questionnaire/service/dto/QuestionnaireLocalizedDTO.java", "hash": "16468310266751804662"}, {"file": "backend/nutrition-master/libs/security/pom.xml", "hash": "9155202328599711169"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/JwtAuthenticationFilter.java", "hash": "11967519449581975084"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/UserDetailsServiceImpl.java", "hash": "14861901821620652317"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/advice/TokenControllerAdvice.java", "hash": "686591174085236730"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/config/AppProperties.java", "hash": "9127039164815812042"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/config/PasswordEncoderConfig.java", "hash": "4979407594229242494"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/config/RestAuthenticationEntryPoint.java", "hash": "9532175598893511871"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/config/SecurityConfig.java", "hash": "5175005656502131997"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/controller/AuthController.java", "hash": "4822186207978481573"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/controller/PasswordRequest.java", "hash": "14733621622675650702"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/controller/UserController.java", "hash": "16740662162683600001"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/dto/JwtAuthenticationResponse.java", "hash": "13219521153532848208"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/dto/LoginRequest.java", "hash": "6787791066915335946"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/dto/PasswordChangeRequest.java", "hash": "18030655482690194291"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/dto/PasswordMatcher.java", "hash": "14957008714454766447"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/dto/SignUpRequest.java", "hash": "7538262819244823492"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/dto/TokenRefreshRequest.java", "hash": "1324985558900022473"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/dto/TokenRefreshResponse.java", "hash": "13259402695464172951"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/dto/UserInfo.java", "hash": "9045266584888086412"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/entities/CustomUser.java", "hash": "15165246619983810370"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/entities/RefreshToken.java", "hash": "6414990285175509167"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/repository/RefreshTokenRepository.java", "hash": "13747727948878874598"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/domain/repository/UserRepository.java", "hash": "7664547130829038305"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/listeners/CustomUserListener.java", "hash": "2246639760633149662"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/listeners/CustomUserListenerHook.java", "hash": "11496429110323356770"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/error/OAuth2AuthenticationProcessingException.java", "hash": "5017443546174935588"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/error/UserAlreadyExistAuthenticationException.java", "hash": "2096975526840273097"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/handlers/OAuth2AuthenticationFailureHandler.java", "hash": "11809265589292706481"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/handlers/OAuth2AuthenticationSuccessHandler.java", "hash": "10482661164768806691"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/provider/SocialProvider.java", "hash": "5510054583511043025"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/service/CustomOAuth2UserService.java", "hash": "8447726266045340089"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/service/CustomOidcUserService.java", "hash": "9244430322610549460"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/service/HttpCookieOAuth2AuthorizationRequestRepository.java", "hash": "887320553562920802"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/service/OAuth2AccessTokenResponseConverterWithDefaults.java", "hash": "4637219702838483186"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/user/FacebookOAuth2UserInfo.java", "hash": "7370793428263113477"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/user/GithubOAuth2UserInfo.java", "hash": "12180382641833799085"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/user/GoogleOAuth2UserInfo.java", "hash": "1714318209083332892"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/user/LinkedinOAuth2UserInfo.java", "hash": "6232068546972735473"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/user/LocalUser.java", "hash": "15327563635505347093"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/user/OAuth2UserInfo.java", "hash": "12767577664102162413"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/oauth/user/OAuth2UserInfoFactory.java", "hash": "1435875351825342449"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/service/AuthEmailService.java", "hash": "15434429647261482072"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/service/CustomUserDetails.java", "hash": "9911784255763840745"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/service/CustomUserService.java", "hash": "11782831769757850790"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/service/RefreshTokenService.java", "hash": "4076868025785390511"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/service/error/TokenRefreshException.java", "hash": "17123382824902673732"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/service/model/CurrentUser.java", "hash": "1978342245752295950"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/utils/GeneralUtils.java", "hash": "16701370766511251968"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/utils/JwtService.java", "hash": "7734297226174049987"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/utils/KeyGenerator.java", "hash": "15097558772181430690"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/utils/PasswordMatches.java", "hash": "6168625694174499261"}, {"file": "backend/nutrition-master/libs/security/src/main/java/com/nutrition/libs/security/utils/PasswordMatchesValidator.java", "hash": "5740443649515436823"}, {"file": "backend/nutrition-master/libs/selenium-shared/pom.xml", "hash": "13588622979231790629"}, {"file": "backend/nutrition-master/libs/selenium-shared/src/main/java/com/nutrition/libs/selenium/AbstractPageObjects.java", "hash": "14945688572474675613"}, {"file": "backend/nutrition-master/libs/selenium-shared/src/main/java/com/nutrition/libs/selenium/Interactions.java", "hash": "9376753635016994792"}, {"file": "backend/nutrition-master/libs/selenium-shared/src/main/java/com/nutrition/libs/selenium/WebDriverManagerProvider.java", "hash": "14140578201163004915"}, {"file": "backend/nutrition-master/libs/selenium-shared/src/main/java/com/nutrition/libs/selenium/config/SeleniumConfiguration.java", "hash": "8542888895481410144"}, {"file": "backend/nutrition-master/libs/shared-domain/pom.xml", "hash": "5917647838321987432"}, {"file": "backend/nutrition-master/libs/shared-domain/src/main/java/com/nutrition/libs/shared/domain/config/SharedDomainConfig.java", "hash": "14619420690333652409"}, {"file": "backend/nutrition-master/libs/shared-domain/src/main/java/com/nutrition/libs/shared/domain/model/BaseEntity.java", "hash": "9322569984443848056"}, {"file": "backend/nutrition-master/libs/shared-domain/src/main/java/com/nutrition/libs/shared/domain/model/MutableEntity.java", "hash": "13013461445645996697"}, {"file": "backend/nutrition-master/libs/shared-domain/src/main/java/com/nutrition/libs/shared/domain/model/UUIDGenerator.java", "hash": "10665781360834726868"}, {"file": "backend/nutrition-master/libs/shared-domain/src/main/java/com/nutrition/libs/shared/domain/user/UserContext.java", "hash": "6043578858194908913"}, {"file": "backend/nutrition-master/libs/shared-domain/src/main/java/com/nutrition/libs/shared/domain/user/UserContextHolder.java", "hash": "9732476848812050581"}, {"file": "backend/nutrition-master/libs/shared-service/pom.xml", "hash": "14893870382930910833"}, {"file": "backend/nutrition-master/libs/shared-service/src/main/java/com/nutrition/libs/shared/service/config/SharedServiceConfig.java", "hash": "1846933489951689410"}, {"file": "backend/nutrition-master/libs/shared-web/pom.xml", "hash": "16373508690575116314"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/java/com/nutrition/libs/shared/web/advice/RestResponseEntityExceptionHandler.java", "hash": "11155650934507542839"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/java/com/nutrition/libs/shared/web/config/WebConfig.java", "hash": "9835829951548063380"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/java/com/nutrition/libs/shared/web/error/ApiConstraintError.java", "hash": "16932338650117888044"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/java/com/nutrition/libs/shared/web/error/ApiError.java", "hash": "8835065216052219872"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/java/com/nutrition/libs/shared/web/error/ApiSubError.java", "hash": "8219611432855537811"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/java/com/nutrition/libs/shared/web/error/ApiValidationError.java", "hash": "13542352547880595908"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/java/com/nutrition/libs/shared/web/error/BadRequestException.java", "hash": "10941659273577290764"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/java/com/nutrition/libs/shared/web/error/advice/RestExceptionHandler.java", "hash": "15102475651828567127"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/java/com/nutrition/libs/shared/web/model/ApiResponse.java", "hash": "2511504130115015062"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/java/com/nutrition/libs/shared/web/utils/CookieUtils.java", "hash": "15212179055393062421"}, {"file": "backend/nutrition-master/libs/shared-web/src/main/resources/messages_en.properties", "hash": "9409056691582407405"}, {"file": "backend/nutrition-master/libs/shared/pom.xml", "hash": "2791186063445714751"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/config/SharedConfig.java", "hash": "2705018940705776050"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/email/EmailService.java", "hash": "8532888273733952225"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/exception/ResourceNotFoundException.java", "hash": "2120981637108446847"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/transaction/SpringTransactionSupport.java", "hash": "8812911170843445822"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/util/annotations/DomainImport.java", "hash": "6628271673576506241"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/util/annotations/InfrastructureImport.java", "hash": "17067773140791020410"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/util/resources/ClassPathResourceFileReader.java", "hash": "10579164887421967616"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/util/xls/ExtractConfiguration.java", "hash": "929823486130716655"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/util/xls/FooterConfig.java", "hash": "13338241493783201794"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/util/xls/HeaderAttribute.java", "hash": "13104740219965227908"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/util/xls/XlsDataExtractor.java", "hash": "16182653099866336051"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/util/xls/XlsDataWriter.java", "hash": "8283367957180546738"}, {"file": "backend/nutrition-master/libs/shared/src/main/java/com/nutrition/libs/shared/util/xls/XlsDataWriterExample.java", "hash": "10097824917877040384"}, {"file": "backend/nutrition-master/libs/shared/src/test/java/com/nutrition/libs/shared/util/xls/XlsDataWriterSimpleTest.java", "hash": "7222044510767902620"}, {"file": "backend/nutrition-master/libs/tazz-crawler/pom.xml", "hash": "14862299948555641081"}, {"file": "backend/nutrition-master/libs/tazz-crawler/src/main/java/com/nutrition/libs/crawler/CrawlerController.java", "hash": "8055173521690862221"}, {"file": "backend/nutrition-master/libs/tazz-crawler/src/main/java/com/nutrition/libs/crawler/config/CrawlerConfig.java", "hash": "13863331743743548430"}, {"file": "backend/nutrition-master/libs/tazz-crawler/src/main/java/com/nutrition/libs/crawler/imports/product/ProductsImporter.java", "hash": "4416271051685970718"}, {"file": "backend/nutrition-master/libs/tazz-crawler/src/main/java/com/nutrition/libs/crawler/model/Product.java", "hash": "16202650110143630156"}, {"file": "backend/nutrition-master/libs/tazz-crawler/src/main/java/com/nutrition/libs/crawler/processor/TazzCrawlingProcess.java", "hash": "10801373722015102478"}, {"file": "backend/nutrition-master/libs/tazz-crawler/src/main/java/com/nutrition/libs/crawler/processor/TazzPageObject.java", "hash": "6623802908493691424"}, {"file": "backend/nutrition-master/libs/test/pom.xml", "hash": "12668920273858288487"}, {"file": "backend/nutrition-master/libs/test/src/main/java/com/nutrition/libs/test/TestBase.java", "hash": "11663725019650303142"}, {"file": "backend/nutrition-master/libs/test/src/main/java/com/nutrition/libs/test/TestBaseConfig.java", "hash": "14890177760141994147"}, {"file": "backend/nutrition-master/libs/test/src/main/java/com/nutrition/libs/test/TestConfig.java", "hash": "17870551224315828182"}, {"file": "backend/nutrition-master/libs/test/src/main/java/com/nutrition/libs/test/TestUtils.java", "hash": "12131569180442411565"}, {"file": "backend/nutrition-master/libs/unused/akka/AkkaJobManager.java", "hash": "1319847144214812192"}, {"file": "backend/nutrition-master/libs/unused/akka/AkkaJobWorker.java", "hash": "11101441615909792648"}, {"file": "backend/nutrition-master/libs/unused/akka/JobProgressReporter.java", "hash": "6069052082197795631"}, {"file": "backend/nutrition-master/libs/unused/akka/springConfig/AppConfiguration.java", "hash": "4697646584556506036"}, {"file": "backend/nutrition-master/libs/unused/akka/springConfig/SpringActorProducer.java", "hash": "5760482988886219429"}, {"file": "backend/nutrition-master/libs/unused/akka/springConfig/SpringExtension.java", "hash": "9308689526664675721"}, {"file": "backend/nutrition-master/libs/unused/service/JobPriorityQueue.java", "hash": "14655260305904082400"}, {"file": "backend/nutrition-master/libs/unused/service/JobService.java", "hash": "958332418095128866"}, {"file": "backend/nutrition-master/libs/user-profile/pom.xml", "hash": "11671565981192770635"}, {"file": "backend/nutrition-master/libs/user-profile/src/main/java/com/nutrition/libs/user/profile/config/UserProfileConfig.java", "hash": "14957064278693609860"}, {"file": "backend/nutrition-master/libs/user-profile/src/main/java/com/nutrition/libs/user/profile/controller/UserProfileRestController.java", "hash": "827596057431320021"}, {"file": "backend/nutrition-master/libs/user-profile/src/main/java/com/nutrition/libs/user/profile/diet/domain/UserProfileInfo.java", "hash": "15769321784335575710"}, {"file": "backend/nutrition-master/libs/user-profile/src/main/java/com/nutrition/libs/user/profile/diet/domain/UserProfileInfoRepository.java", "hash": "11996806698091383486"}, {"file": "backend/nutrition-master/libs/user-profile/src/main/java/com/nutrition/libs/user/profile/dto/UserData.java", "hash": "16298016415922532579"}, {"file": "backend/nutrition-master/libs/user-profile/src/main/java/com/nutrition/libs/user/profile/dto/UserPersonalDetails.java", "hash": "13037223387128592239"}, {"file": "backend/nutrition-master/libs/user-profile/src/main/java/com/nutrition/libs/user/profile/lib/listener/UserProfileRemoveHook.java", "hash": "6395886056121316352"}, {"file": "backend/nutrition-master/libs/user-profile/src/main/java/com/nutrition/libs/user/profile/service/UserProfileInfoService.java", "hash": "10329910653737282336"}, {"file": "backend/nutrition-master/libs/video/pom.xml", "hash": "3671356551409495527"}, {"file": "backend/nutrition-master/libs/video/src/main/java/com/nutrition/libs/video/config/VideoConfiguration.java", "hash": "902306710522300404"}, {"file": "backend/nutrition-master/libs/video/src/main/java/com/nutrition/libs/video/controller/VideoController.java", "hash": "14312024670071817378"}, {"file": "backend/nutrition-master/libs/video/src/main/java/com/nutrition/libs/video/domain/Video.java", "hash": "7216508725142314961"}, {"file": "backend/nutrition-master/libs/video/src/main/java/com/nutrition/libs/video/domain/VideoStatus.java", "hash": "9619734490564292589"}, {"file": "backend/nutrition-master/libs/video/src/main/java/com/nutrition/libs/video/repository/VideoRepository.java", "hash": "6550095605288992945"}, {"file": "backend/nutrition-master/libs/video/src/main/java/com/nutrition/libs/video/service/VideoProcessingService.java", "hash": "1199024866351506268"}, {"file": "backend/nutrition-master/nutrition-web/.gitignore", "hash": "7919888732090103848"}, {"file": "backend/nutrition-master/nutrition-web/.mvn/wrapper/maven-wrapper.properties", "hash": "3115364285690843818"}, {"file": "backend/nutrition-master/nutrition-web/mvnw", "hash": "17157330811278791850"}, {"file": "backend/nutrition-master/nutrition-web/mvnw.cmd", "hash": "17655327423869235761"}, {"file": "backend/nutrition-master/nutrition-web/pom.xml", "hash": "7628934504194017498"}, {"file": "backend/nutrition-master/nutrition-web/src/main/java/com/nutrition/nutrition_web/HomeController.java", "hash": "9953803828356779674"}, {"file": "backend/nutrition-master/nutrition-web/src/main/java/com/nutrition/nutrition_web/NutritionWebApplication.java", "hash": "6541528476393784109"}, {"file": "backend/nutrition-master/nutrition-web/src/main/java/com/nutrition/nutrition_web/TestController.java", "hash": "15930008256368089282"}, {"file": "backend/nutrition-master/nutrition-web/src/main/java/com/nutrition/nutrition_web/config/ApplicationConfig.java", "hash": "11050017767930983694"}, {"file": "backend/nutrition-master/nutrition-web/src/main/java/com/nutrition/nutrition_web/config/VideoStaticResourceConfig.java", "hash": "722403644319975983"}, {"file": "backend/nutrition-master/nutrition-web/src/main/java/com/nutrition/runners/EatnTrackImport.java", "hash": "13779939437841411685"}, {"file": "backend/nutrition-master/nutrition-web/src/main/java/com/nutrition/runners/ProductsImport.java", "hash": "13200776575834421524"}, {"file": "backend/nutrition-master/nutrition-web/src/main/java/com/nutrition/runners/UsfdaImport.java", "hash": "6221047486630281026"}, {"file": "backend/nutrition-master/nutrition-web/src/main/java/com/nutrition/runners/config/ApplicationConfig.java", "hash": "3655730473549294756"}, {"file": "backend/nutrition-master/nutrition-web/src/main/resources/application-dev.yml", "hash": "3244421341483603138"}, {"file": "backend/nutrition-master/nutrition-web/src/main/resources/application.yml", "hash": "15957033124131014015"}, {"file": "backend/nutrition-master/nutrition-web/src/main/resources/eatntrack_products.xlsx", "hash": "93825147292297077"}, {"file": "backend/nutrition-master/nutrition-web/src/main/resources/import/US FDA.xlsx", "hash": "1963008655297161468"}, {"file": "backend/nutrition-master/nutrition-web/src/main/resources/import/Vision.xlsx", "hash": "17554000232817440124"}, {"file": "backend/nutrition-master/nutrition-web/src/main/resources/logback.xml", "hash": "927703879530040983"}, {"file": "backend/nutrition-master/nutrition-web/src/test/java/com/nutrition/nutrition_web/NutritionWebApplicationTests.java", "hash": "5615385123712898260"}, {"file": "capacitor.config.ts", "hash": "4721034128294280644"}, {"file": "command", "hash": "3244421341483603138"}, {"file": "docker/docker-compose.yml", "hash": "14959295156565285478"}, {"file": "icons/icon-128.webp", "hash": "10783644456686195366"}, {"file": "icons/icon-192.webp", "hash": "18359614726549330319"}, {"file": "icons/icon-256.webp", "hash": "783981043046901646"}, {"file": "icons/icon-48.webp", "hash": "3022439220218111632"}, {"file": "icons/icon-512.webp", "hash": "8089180825128248103"}, {"file": "icons/icon-72.webp", "hash": "13275884455763975580"}, {"file": "icons/icon-96.webp", "hash": "3145369309634049386"}, {"file": "jest.config.ts", "hash": "7544409015863045561"}, {"file": "jest.preset.js", "hash": "7473220183190921152"}, {"file": "libs/nx_command.md", "hash": "14102321516017699386"}, {"file": "migrations.json", "hash": "14233580884588623465"}, {"file": "nutrition.iml", "hash": "2871715169684900361"}, {"file": "nx.json", "hash": "7904669265755221902"}, {"file": "package-lock.json", "hash": "3431159306910121492"}, {"file": "package.json", "hash": "15031448514664147989"}, {"file": "pom.xml", "hash": "6390342142859713787"}, {"file": "proxy-mobile.conf.js", "hash": "15451393044201354916"}, {"file": "proxy.conf.js", "hash": "2882304342236463269"}, {"file": "proxy.conf.json", "hash": "8832136744935079770"}, {"file": "resources/google-fit-response.json", "hash": "12499585757734491179"}, {"file": "tsconfig.base.json", "hash": "9384805537274837231"}]}}