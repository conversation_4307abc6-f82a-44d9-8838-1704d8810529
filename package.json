{"name": "@nutrition/source", "version": "0.0.0", "license": "MIT", "scripts": {"start": "nx serve nutrition --port=4200 --proxy-config proxy.conf.js", "start-for-mobile": "nx serve nutrition --host=0.0.0.0 --port=4200 --proxy-config proxy-mobile.conf.js", "start admin": "nx serve nutrition --proxy-config proxy.conf.js", "build": "nx build nutrition", "build admin": "nx build nutrition-admin", "cap-sync-android": "nx run nutrition:cap-sync-android"}, "private": true, "dependencies": {"@angular/animations": "19.2.2", "@angular/cdk": "19.2.3", "@angular/common": "19.2.2", "@angular/compiler": "19.2.2", "@angular/core": "19.2.2", "@angular/forms": "19.2.2", "@angular/material": "19.2.3", "@angular/platform-browser": "19.2.2", "@angular/platform-browser-dynamic": "19.2.2", "@angular/router": "19.2.2", "@capacitor/android": "^6.2.0", "@capacitor/app": "^6.0.1", "@capacitor/camera": "^6.1.2", "@capacitor/core": "6.2.0", "@capacitor/filesystem": "^6.0.2", "@capacitor/haptics": "^6.0.1", "@capacitor/ios": "^6.2.0", "@capacitor/keyboard": "^6.0.2", "@capacitor/splash-screen": "^6.0.3", "@capacitor/status-bar": "^6.0.1", "@ionic/angular": "^8.5.3", "@ionic/core": "^8.5.3", "@ionic/pwa-elements": "^3.3.0", "@ionic/storage-angular": "^4.0.0", "@ng-icons/core": "^30.2.0", "@ng-icons/material-icons": "^30.2.0", "@ngrx/operators": "^19.0.0", "@ngrx/signals": "^19.0.0", "@ngrx/store-devtools": "^19.0.0", "@ngx-translate/core": "^16.0.3", "@ngx-translate/http-loader": "^16.0.0", "@nx/angular": "20.5.0", "d3": "^7.9.0", "install": "^0.13.0", "interactjs": "^1.10.27", "ionicons": "^7.4.0", "jwt-decode": "^4.0.0", "material-design-icons": "^3.0.1", "ngx-plyr": "^4.0.1", "ngxtension": "^4.2.1", "npm": "^10.9.0", "plyr": "^3.7.8", "rxjs": "~7.8.0", "swiper": "^11.2.2", "tslib": "^2.3.0", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "19.2.3", "@angular-devkit/core": "19.2.3", "@angular-devkit/schematics": "19.2.3", "@angular-eslint/eslint-plugin": "19.2.0", "@angular-eslint/eslint-plugin-template": "19.2.0", "@angular-eslint/template-parser": "19.2.0", "@angular/cli": "^19.2.3", "@angular/compiler-cli": "19.2.2", "@angular/language-service": "19.2.2", "@angular/localize": "19.2.2", "@capacitor/assets": "^3.0.5", "@capacitor/cli": "^6.1.2", "@capacitor/core": "^6.1.2", "@ionic/angular-toolkit": "^11.0.1", "@nx/eslint": "20.5.0", "@nx/eslint-plugin": "20.5.0", "@nx/jest": "20.5.0", "@nx/js": "20.5.0", "@nx/web": "20.5.0", "@nx/workspace": "20.5.0", "@schematics/angular": "19.2.3", "@stagewise/toolbar": "^0.2.1", "@swc-node/register": "1.9.2", "@swc/core": "1.5.7", "@swc/helpers": "0.5.13", "@types/d3": "^7.4.3", "@types/jest": "29.5.13", "@types/node": "18.16.9", "@typescript-eslint/eslint-plugin": "7.16.0", "@typescript-eslint/parser": "7.16.0", "@typescript-eslint/utils": "^7.16.0", "eslint": "~8.57.0", "eslint-config-prettier": "^9.0.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-preset-angular": "14.4.2", "nx": "20.5.0", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "5.7.3"}}