<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      Inbox
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-refresher slot="fixed" (ionRefresh)="refresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">
        Inbox
      </ion-title>
    </ion-toolbar>
  </ion-header>
  
  <ion-list>
    <app-message *ngFor="let message of getMessages()" [message]="message"></app-message>
  </ion-list>
</ion-content>