import { Route } from '@angular/router';
import { ProductListComponent } from './features/products/product-list/product-list.component';
import { ProductDetailsComponent } from './features/products/product-details/product-details.component';
import { MainLayoutComponent } from './shared/layout/main-layout/main-layout.component';
import { ProductDashboardComponent } from './features/products/product-dashboard/product-dashboard.component';
import { ProductsCompareComponent } from './features/products/products-compare/products-compare.component';
import {
  authGuard,
  LoginComponent,
  PasswordRecoveryComponent,
  RegisterComponent,
  RequestPasswordRecoveryComponent
} from '@nutrition/auth';
import { compositeGuard } from '@nutrition/shared';

// Use dynamic import instead of a static one:
const firstLoginGuard = () =>
  import('@nutrition/user-profile').then(m => m.firstLoginGuard);


export const appRoutes: Route[] = [
  { path: 'login', component: LoginComponent },
  { path: 'register', component: RegisterComponent },
  { path: 'request-password-recovery', component: RequestPasswordRecoveryComponent },
  { path: 'recover-password', component: PasswordRecoveryComponent },
  {
    path: 'playground',
    pathMatch: 'full',
    loadComponent: () => import('@nutrition/playground').then(c => c.PlaygroundComponent)
  },
  { path: 'playground/camera', loadComponent: () => import('@nutrition/camera-test').then(c => c.CameraTestComponent) },
  { path: '',
    canMatch: [compositeGuard([authGuard])],
    children:[
      { path: '', redirectTo: 'main-tabs', pathMatch: 'full' }, // Default redirect
      {
        path: 'main-tabs',
        component: MainLayoutComponent,
        children: [
          { path: '', redirectTo: 'home', pathMatch: 'full' },
          {
            path: 'home',
            children: [{ path: '', loadComponent: () => import('@nutrition/journal').then(c => c.JournalComponent) }]
          },
          {
            path: 'plans',
            children: [{ path: '', loadComponent: () => import('@nutrition/plans').then(c => c.PlansComponent) }]
          },
          {
            path: 'plans/food-plans-dashboard',
            children: [{
              path: '',
              loadComponent: () => import('@nutrition/plans').then(c => c.PlansFoodDashboardComponent)
            }]
          },
          {
            path: 'plans/workout-plans-dashboard',
            children: [{
              path: '',
              loadComponent: () => import('@nutrition/plans').then(c => c.PlansWorkoutDashboardComponent)
            }]
          },
          {
            path: 'plans/mental-plans-dashboard',
            children: [{
              path: '',
              loadComponent: () => import('@nutrition/plans').then(c => c.PlansMentalDashboardComponent)
            }]
          },
          { path: 'explorer', loadChildren: ()=> import('@nutrition/explorer').then(c => c.EXPLORER_ROUTES) },
          { path: 'experts', loadComponent: () => import('@nutrition/experts').then(c => c.ExpertsComponent) },
          { path: 'shop', loadComponent: () => import('@nutrition/shop').then(c => c.ShopComponent) },
          { path: 'products-dashboard', component: ProductDashboardComponent },
          { path: 'products', component: ProductListComponent },
          { path: 'products/:id', component: ProductDetailsComponent },
          { path: 'products-compare', component: ProductsCompareComponent },
          { path: 'videos', loadChildren: () => import('./features/videos/video.routes').then(r => r.VIDEO_ROUTES) },
        ]
      },
      { path: 'user-profile', loadChildren: ()=> import('@nutrition/user-profile').then(c => c.USER_PROFILE_ROUTES) },
      { path: '**', redirectTo: 'main-tabs', pathMatch: 'full' }
    ]
  },
// Default route
];
