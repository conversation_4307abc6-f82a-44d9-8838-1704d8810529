@import 'vars';
:root {
  --ion-color-primary: #1b4f41;
  --ion-color-primary-rgb: 27, 79, 65;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #184639;
  --ion-color-primary-tint: #326154;

  --ion-color-secondary: #fdae35;
  --ion-color-secondary-rgb: 253, 174, 53;
  --ion-color-secondary-contrast: #000000;
  --ion-color-secondary-contrast-rgb: 0, 0, 0;
  --ion-color-secondary-shade: #df992f;
  --ion-color-secondary-tint: #fdb649;

  --ion-color-tertiary: #85a947;
  --ion-color-tertiary-rgb: 133, 169, 71;
  --ion-color-tertiary-contrast: #000000;
  --ion-color-tertiary-contrast-rgb: 0, 0, 0;
  --ion-color-tertiary-shade: #75953e;
  --ion-color-tertiary-tint: #91b259;

  --ion-color-success: #2dd55b;
  --ion-color-success-rgb: 45, 213, 91;
  --ion-color-success-contrast: #000000;
  --ion-color-success-contrast-rgb: 0, 0, 0;
  --ion-color-success-shade: #28bb50;
  --ion-color-success-tint: #42d96b;

  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb: 255, 196, 9;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;

  --ion-color-danger: #c5000f;
  --ion-color-danger-rgb: 197, 0, 15;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #ad000d;
  --ion-color-danger-tint: #cb1a27;

  --ion-color-light: #f6f8fc;
  --ion-color-light-rgb: 246, 248, 252;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d8dade;
  --ion-color-light-tint: #f7f9fc;

  --ion-color-medium: #5f5f5f;
  --ion-color-medium-rgb: 95, 95, 95;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #545454;
  --ion-color-medium-tint: #6f6f6f;

  --ion-color-dark: #2f2f2f;
  --ion-color-dark-rgb: 47, 47, 47;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #292929;
  --ion-color-dark-tint: #444444;

  --ion-background-color: #F7F7F7;
	--ion-background-color-rgb: 247,247,247;

	--ion-text-color: #000000;
	--ion-text-color-rgb: 0,0,0;

	--ion-text-color-step-50: #0c0c0c;
	--ion-text-color-step-100: #191919;
	--ion-text-color-step-150: #252525;
	--ion-text-color-step-200: #313131;
	--ion-text-color-step-250: #3e3e3e;
	--ion-text-color-step-300: #4a4a4a;
	--ion-text-color-step-350: #565656;
	--ion-text-color-step-400: #636363;
	--ion-text-color-step-450: #6f6f6f;
	--ion-text-color-step-500: #7c7c7c;
	--ion-text-color-step-550: #888888;
	--ion-text-color-step-600: #949494;
	--ion-text-color-step-650: #a1a1a1;
	--ion-text-color-step-700: #adadad;
	--ion-text-color-step-750: #b9b9b9;
	--ion-text-color-step-800: #c6c6c6;
	--ion-text-color-step-850: #d2d2d2;
	--ion-text-color-step-900: #dedede;
	--ion-text-color-step-950: #ebebeb;

	--ion-background-color-step-50: #ebebeb;
	--ion-background-color-step-100: #dedede;
	--ion-background-color-step-150: #d2d2d2;
	--ion-background-color-step-200: #c6c6c6;
	--ion-background-color-step-250: #b9b9b9;
	--ion-background-color-step-300: #adadad;
	--ion-background-color-step-350: #a1a1a1;
	--ion-background-color-step-400: #949494;
	--ion-background-color-step-450: #888888;
	--ion-background-color-step-500: #7c7c7c;
	--ion-background-color-step-550: #6f6f6f;
	--ion-background-color-step-600: #636363;
	--ion-background-color-step-650: #565656;
	--ion-background-color-step-700: #4a4a4a;
	--ion-background-color-step-750: #3e3e3e;
	--ion-background-color-step-800: #313131;
	--ion-background-color-step-850: #252525;
	--ion-background-color-step-900: #191919;
	--ion-background-color-step-950: #0c0c0c;
 // Define the maximum pixel value for the classes.
 --ion-color-purple: #b200ed;
 --ion-color-purple-rgb: 178,0,237;
 --ion-color-purple-contrast: #ffffff;
 --ion-color-purple-contrast-rgb: 255,255,255;
 --ion-color-purple-shade: #9d00d1;
 --ion-color-purple-tint: #ba1aef;

 --background-transparency: rgba(255, 255, 255, 0.08);
 --ion-color-success-rgba: rgba(45, 213, 91);
 --ion-color-warning-rgba: rgba(255, 196, 9);
 --ion-color-danger-rgba: rgba(197, 0, 15);
}


.ion-color-purple {
	--ion-color-base: var(--ion-color-purple);
	--ion-color-base-rgb: var(--ion-color-purple-rgb);
	--ion-color-contrast: var(--ion-color-purple-contrast);
	--ion-color-contrast-rgb: var(--ion-color-purple-contrast-rgb);
	--ion-color-shade: var(--ion-color-purple-shade);
	--ion-color-tint: var(--ion-color-purple-tint);
}

:root {
	--ion-background-color-active: #{$background-active};
}