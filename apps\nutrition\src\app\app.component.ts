import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import {TranslateService} from "@ngx-translate/core";




@Component({
  standalone: true,
  imports: [IonicModule, RouterModule],
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent {
  title = 'nutrition';

  constructor(private translate: TranslateService) {
    this.translate.addLangs(['ro', 'en']);
    this.translate.setDefaultLang('ro');
    this.translate.use('ro');
  }
}
