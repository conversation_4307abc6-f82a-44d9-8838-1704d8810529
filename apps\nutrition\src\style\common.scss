
@import 'vars';
@import 'commons/chips';
@import 'commons/list';
@import 'commons/questions-lifestyle';

ion-searchbar {
  -webkit-padding-start: 0px !important;
  padding-inline-start: 0px !important;
  -webkit-padding-end: 0px !important;
  padding-inline-end: 0px !important;
  padding-top: 0px !important;
  padding-bottom: 0px !important;
  --background: white !important;
}

.green-pill{
  background-color: var(--color-green);
  color: white;
  display: inline-flex;
  border-radius: 12px;
  padding: 4px;
}
.danger{
  background-color: var(--danger-background-color);
  color: var(--ion-color-danger);
  //border-left: 4px solid var(--ion-color-danger);
}
.flex-container, .flex{
  display: flex;
}
.row{
  flex-direction: row;
}
.column{
  flex-direction: column;
}
.w-100{
  width: 100%;
}
.auth-container {
  display: flex;
  flex-direction: column;
  //  align-items: center;
  //  justify-content: center;
  flex-grow: 1;
  form{
    width: 100%;
    p{
      color: white;
      display: flex;
      justify-content: center;
    }
  }
  label{
    color: white;
  }
  ion-input{
    --background: rgba(255,255,255,0.6);
    --font-size: 1.6rem;
  }
  ion-button{
    margin: 10px 0px 0px 0px;
  }
  @media (min-width: 768px){

    position: relative;
    width: 100vw; /* Full viewport width */
    height: 100vh; /* Full viewport height */
    background:
      url('/heart-shape-by-various-vegetables-fruits.jpg') center center / contain no-repeat; /* Centered image */
    background-repeat: no-repeat;
    background-size: cover; /* Keep aspect ratio */
    background-attachment: fixed;
    .auth-form{
      position: absolute;
      top: 50%; /* Move it to the center */
      bottom: 50%; /* Move it to the center horizontally */
      transform: translateY(-50%, -50%);
      right: 20%;
      .alert-danger, .alert{
        background-color: var(--ion-color-danger);
      }
    }
  }
  @media (max-width: 768px) {

    background: url('/freepik__background__57850.png') center center / contain no-repeat;
    background-size: cover;
    //background-position: top left;
    background-repeat: no-repeat;
    height: 100vh; /* Ensure it covers full height */
    width: 100vw;
    background-attachment: fixed;
    .auth-form{
      position: absolute;
      bottom: 10%;
      left: 50%;
      right: 50%;
      transform: translate(-50%);
      display: flex;
      width: 300px;
      .form-group{
        margin-top: 10px;
      }
      a{
        color: white;
      }
    }
  }
}

/* Reusable form styles */
.form-input-rounded {
  ion-item {
    margin: 8px 0;
    --padding-start: 16px;
    --padding-end: 16px;
    --background: transparent;
    --border-radius: 20px;
    --border-width: 1px;
    --highlight-height: 0;
    --border-style: none;
    --border-bottom: none;
    --border-bottom-color: transparent;
    --border-bottom-style: none;
    --border-bottom-width: 0;
  }

  ion-item::part(native) {
    --border-style: none;
    border-radius: 20px;
    margin: 8px 0;
    .input-wrapper{
      border-radius: map-get($border-radii, 's') !important;
    }
  }


  ion-input, ion-select, ion-datetime {
    --padding-end: 16px;
    .input-wrapper{
      border-radius: map-get($border-radii, 's') !important;
    }
  }

  &.focused {
    ion-item {
      --border-color: var(--ion-color-primary);
      box-shadow: 0 0 8px rgba(var(--ion-color-primary-rgb), 0.3);
    }
  }
}

/* Card styles */
.form-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-top: 16px;
}

/* Button container styles */
.button-container-spaced {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
  padding: 0 16px;

}

ion-list{
  background: none !important;
}

ion-card{
  border-radius: 16px;
}

.input-fill-solid,.input-fill-solid.sc-ion-input-md-h{
  --border-radius: #{map-get($border-radii, 'm')} !important;
  label{
    border-end-end-radius: #{map-get($border-radii, 'm')} !important;
    border-end-start-radius: #{map-get($border-radii, 'm')} !important;
    .input-highlight{
      display: none !important;
    }
  }
}
.input-fill-solid.has-focus, .input-fill-solid.has-focus.sc-ion-input-md-h{
  --border-color: var(--ion-color-primary) !important;
  --border-width: 2px !important;
}
ion-input, ion-select, ion-datetime{
  margin-bottom: 16px;
}

ion-select{
  --border-radius: map-get($border-radii, 's') !important;
  .select-wrapper{
    border-radius: map-get($border-radii, 's') !important;
    border-start-start-radius: map-get($border-radii, 's') !important;
    border-start-end-radius: map-get($border-radii, 's') !important;
    border-end-end-radius: map-get($border-radii, 's') !important;
    border-end-start-radius: map-get($border-radii, 's') !important; 
  }
}
.select-wrapper{
  border-radius: map-get($border-radii, 's') !important;
  border-start-start-radius: map-get($border-radii, 's') !important;
  border-start-end-radius: map-get($border-radii, 's') !important;
  border-end-end-radius: map-get($border-radii, 's') !important;
  border-end-start-radius: map-get($border-radii, 's') !important; 
}

/* Floating tab bar styles */
ion-tab-bar {
  --background: white;
  position: fixed;
  bottom: 16px;
  left: 16px;
  right: 16px;
  width: auto;
  border-radius: 20px;
  padding: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);

  ion-tab-button {
    --color: #666;
    --color-selected: var(--ion-color-primary);
    font-size: 12px;
    letter-spacing: 0;

    &::before {
      display: none;
    }

    &.tab-selected {
      ion-icon {
        transform: scale(1.1);
      }
    }

    ion-icon {
      transition: transform 0.2s ease;
    }

    ion-label {
      font-size: 12px;
      font-weight: 500;
      margin-top: 4px;
    }
  }
  
}
ion-card{
  --ion-card-background: white;
}

.health-score-modal {
  --height: auto;
  align-items: end;
}

ion-toolbar{
  ion-back-button{
    text-transform: none;
  }
}
ion-segment-button{
  text-transform: none;
}
ion-button{
  text-transform: none;
}
.ion-flex{
  display: flex;
}
.ion-flex-row{
  display: flex;
  flex-direction: row;
}
.ion-flex-column{
  display: flex;
  flex-direction: column;
}
.ion-flex-grow{
  flex-grow: 1;
}

.rounded-button{
  --border-radius: #{map-get($border-radii, 'm')} !important;
}

.rounded-inner-card{
  border-radius: map-get($border-radii, 'm') !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

}

.hide-scrollbar {
  overflow: auto; /* or scroll */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.card-with-transparency{
  background-color: var(--background-transparency);
  color: white;
  border-radius: map-get($border-radii, 'm');
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
